<?php $__env->startSection('title'); ?>
    <?php echo e(trans('sales::app.performance.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="flex flex-col gap-4">
        <?php echo view_render_event('sales.performance.index.header.before'); ?>


        <!-- Page Header -->
        <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300">
            <div class="flex flex-col gap-2">
                <div class="flex cursor-pointer items-center">
                    <?php if (isset($component)) { $__componentOriginal477735b45b070062c5df1d72c43d48f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal477735b45b070062c5df1d72c43d48f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.breadcrumbs.index','data' => ['name' => 'sales.performance']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'sales.performance']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal477735b45b070062c5df1d72c43d48f5)): ?>
<?php $attributes = $__attributesOriginal477735b45b070062c5df1d72c43d48f5; ?>
<?php unset($__attributesOriginal477735b45b070062c5df1d72c43d48f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal477735b45b070062c5df1d72c43d48f5)): ?>
<?php $component = $__componentOriginal477735b45b070062c5df1d72c43d48f5; ?>
<?php unset($__componentOriginal477735b45b070062c5df1d72c43d48f5); ?>
<?php endif; ?>
                </div>

                <div class="text-xl font-bold dark:text-white">
                    <?php echo e(trans('sales::app.performance.title')); ?>

                </div>
            </div>

            <div class="flex items-center gap-x-2.5">
                <!-- Filter Controls -->
                <v-performance-filters></v-performance-filters>
            </div>
        </div>

        <?php echo view_render_event('sales.performance.index.header.after'); ?>


        <!-- Performance Dashboard -->
        <v-performance-dashboard>
            <div class="flex flex-col gap-4">
                <!-- Shimmer for Overview Cards -->
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <?php for($i = 0; $i < 4; $i++): ?>
                        <?php if (isset($component)) { $__componentOriginal749a64c217134518f8b1b509819c7e9f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal749a64c217134518f8b1b509819c7e9f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.dashboard.index.over-all','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.dashboard.index.over-all'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal749a64c217134518f8b1b509819c7e9f)): ?>
<?php $attributes = $__attributesOriginal749a64c217134518f8b1b509819c7e9f; ?>
<?php unset($__attributesOriginal749a64c217134518f8b1b509819c7e9f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal749a64c217134518f8b1b509819c7e9f)): ?>
<?php $component = $__componentOriginal749a64c217134518f8b1b509819c7e9f; ?>
<?php unset($__componentOriginal749a64c217134518f8b1b509819c7e9f); ?>
<?php endif; ?>
                    <?php endfor; ?>
                </div>

                <!-- Shimmer for Charts -->
                <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                    <?php if (isset($component)) { $__componentOriginal9c2401f601e6d79e253560e6742ec3e4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9c2401f601e6d79e253560e6742ec3e4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.dashboard.index.revenue','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.dashboard.index.revenue'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9c2401f601e6d79e253560e6742ec3e4)): ?>
<?php $attributes = $__attributesOriginal9c2401f601e6d79e253560e6742ec3e4; ?>
<?php unset($__attributesOriginal9c2401f601e6d79e253560e6742ec3e4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9c2401f601e6d79e253560e6742ec3e4)): ?>
<?php $component = $__componentOriginal9c2401f601e6d79e253560e6742ec3e4; ?>
<?php unset($__componentOriginal9c2401f601e6d79e253560e6742ec3e4); ?>
<?php endif; ?>
                    <?php if (isset($component)) { $__componentOriginal9c2401f601e6d79e253560e6742ec3e4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9c2401f601e6d79e253560e6742ec3e4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.dashboard.index.revenue','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.dashboard.index.revenue'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9c2401f601e6d79e253560e6742ec3e4)): ?>
<?php $attributes = $__attributesOriginal9c2401f601e6d79e253560e6742ec3e4; ?>
<?php unset($__attributesOriginal9c2401f601e6d79e253560e6742ec3e4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9c2401f601e6d79e253560e6742ec3e4)): ?>
<?php $component = $__componentOriginal9c2401f601e6d79e253560e6742ec3e4; ?>
<?php unset($__componentOriginal9c2401f601e6d79e253560e6742ec3e4); ?>
<?php endif; ?>
                </div>

                <!-- Shimmer for Leaderboard -->
                <?php if (isset($component)) { $__componentOriginal9c2401f601e6d79e253560e6742ec3e4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9c2401f601e6d79e253560e6742ec3e4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.dashboard.index.revenue','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.dashboard.index.revenue'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9c2401f601e6d79e253560e6742ec3e4)): ?>
<?php $attributes = $__attributesOriginal9c2401f601e6d79e253560e6742ec3e4; ?>
<?php unset($__attributesOriginal9c2401f601e6d79e253560e6742ec3e4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9c2401f601e6d79e253560e6742ec3e4)): ?>
<?php $component = $__componentOriginal9c2401f601e6d79e253560e6742ec3e4; ?>
<?php unset($__componentOriginal9c2401f601e6d79e253560e6742ec3e4); ?>
<?php endif; ?>
            </div>
        </v-performance-dashboard>

        <?php echo view_render_event('sales.performance.index.content.after'); ?>

    </div>
<?php $__env->stopSection(); ?>

<?php if (! $__env->hasRenderedOnce('b5b2fd34-ddf1-48b9-ab5e-edea7008a73e')): $__env->markAsRenderedOnce('b5b2fd34-ddf1-48b9-ab5e-edea7008a73e');
$__env->startPush('scripts'); ?>
    <script
        type="text/x-template"
        id="v-performance-filters-template"
    >
        <div class="flex items-center gap-2">
            <!-- Period Filter -->
            <select
                v-model="filters.period"
                @change="applyFilters"
                class="rounded border border-gray-300 px-3 py-1 text-sm dark:border-gray-600 dark:bg-gray-800"
            >
                <option value="daily"><?php echo e(trans('admin::app.layouts.daily')); ?></option>
                <option value="weekly"><?php echo e(trans('admin::app.layouts.weekly')); ?></option>
                <option value="monthly"><?php echo e(trans('admin::app.layouts.monthly')); ?></option>
                <option value="quarterly"><?php echo e(trans('admin::app.layouts.quarterly')); ?></option>
            </select>

            <!-- Date Range -->
            <input
                type="date"
                v-model="filters.date_from"
                @change="applyFilters"
                class="rounded border border-gray-300 px-3 py-1 text-sm dark:border-gray-600 dark:bg-gray-800"
            />
            <span class="text-gray-500">to</span>
            <input
                type="date"
                v-model="filters.date_to"
                @change="applyFilters"
                class="rounded border border-gray-300 px-3 py-1 text-sm dark:border-gray-600 dark:bg-gray-800"
            />
        </div>
    </script>

    <script
        type="text/x-template"
        id="v-performance-dashboard-template"
    >
        <div v-if="isLoading">
            <slot></slot>
        </div>

        <div v-else class="flex flex-col gap-4">
            <!-- Overview Cards -->
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                <!-- Total Achievement -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo e(trans('sales::app.performance.achievement-rate')); ?></p>
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ summary.average_achievement }}%</p>
                        </div>
                        <div class="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
                            <span class="icon-target text-xl text-blue-600 dark:text-blue-400"></span>
                        </div>
                    </div>
                </div>

                <!-- Conversion Rate -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo e(trans('admin::app.layouts.conversion-rate')); ?></p>
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ summary.average_conversion }}%</p>
                        </div>
                        <div class="rounded-full bg-green-100 p-3 dark:bg-green-900">
                            <span class="icon-leads text-xl text-green-600 dark:text-green-400"></span>
                        </div>
                    </div>
                </div>

                <!-- Total Target -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo e(trans('admin::app.layouts.total-target')); ?></p>
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ formatCurrency(summary.total_target_amount) }}</p>
                        </div>
                        <div class="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900">
                            <span class="icon-quote text-xl text-yellow-600 dark:text-yellow-400"></span>
                        </div>
                    </div>
                </div>

                <!-- Total Achieved -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo e(trans('admin::app.layouts.total-achieved')); ?></p>
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ formatCurrency(summary.total_achieved_amount) }}</p>
                        </div>
                        <div class="rounded-full bg-purple-100 p-3 dark:bg-purple-900">
                            <span class="icon-activity text-xl text-purple-600 dark:text-purple-400"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                <!-- Target vs Actual Chart -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="mb-4 flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
                            <?php echo e(trans('sales::app.performance.target-vs-actual')); ?>

                        </h3>
                    </div>

                    <?php if (isset($component)) { $__componentOriginalf196fc0ab37fc50ba89798b7f8a09a8b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf196fc0ab37fc50ba89798b7f8a09a8b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.charts.bar','data' => [':labels' => 'targetVsActualLabels',':datasets' => 'targetVsActualDatasets','aspectRatio' => 2]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::charts.bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([':labels' => 'targetVsActualLabels',':datasets' => 'targetVsActualDatasets','aspect-ratio' => 2]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf196fc0ab37fc50ba89798b7f8a09a8b)): ?>
<?php $attributes = $__attributesOriginalf196fc0ab37fc50ba89798b7f8a09a8b; ?>
<?php unset($__attributesOriginalf196fc0ab37fc50ba89798b7f8a09a8b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf196fc0ab37fc50ba89798b7f8a09a8b)): ?>
<?php $component = $__componentOriginalf196fc0ab37fc50ba89798b7f8a09a8b; ?>
<?php unset($__componentOriginalf196fc0ab37fc50ba89798b7f8a09a8b); ?>
<?php endif; ?>
                </div>

                <!-- Performance Trends -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="mb-4 flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
                            <?php echo e(trans('sales::app.performance.progress')); ?>

                        </h3>
                    </div>

                    <?php if (isset($component)) { $__componentOriginalb8c5a56cc7464ac234c10f1e17210837 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb8c5a56cc7464ac234c10f1e17210837 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.charts.line','data' => [':labels' => 'trendsLabels',':datasets' => 'trendsDatasets','aspectRatio' => 2]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::charts.line'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([':labels' => 'trendsLabels',':datasets' => 'trendsDatasets','aspect-ratio' => 2]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb8c5a56cc7464ac234c10f1e17210837)): ?>
<?php $attributes = $__attributesOriginalb8c5a56cc7464ac234c10f1e17210837; ?>
<?php unset($__attributesOriginalb8c5a56cc7464ac234c10f1e17210837); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb8c5a56cc7464ac234c10f1e17210837)): ?>
<?php $component = $__componentOriginalb8c5a56cc7464ac234c10f1e17210837; ?>
<?php unset($__componentOriginalb8c5a56cc7464ac234c10f1e17210837); ?>
<?php endif; ?>
                </div>
            </div>

            <!-- Leaderboard -->
            <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                <div class="mb-4 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
                        <?php echo e(trans('sales::app.performance.leaderboard')); ?>

                    </h3>

                    <div class="flex items-center gap-2">
                        <select
                            v-model="leaderboardType"
                            @change="loadLeaderboard"
                            class="rounded border border-gray-300 px-3 py-1 text-sm dark:border-gray-600 dark:bg-gray-800"
                        >
                            <option value="individual"><?php echo e(trans('sales::app.performance.individual')); ?></option>
                            <option value="team"><?php echo e(trans('sales::app.performance.team')); ?></option>
                            <option value="region"><?php echo e(trans('sales::app.performance.region')); ?></option>
                        </select>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-gray-200 dark:border-gray-700">
                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-600 dark:text-gray-300">
                                    <?php echo e(trans('sales::app.performance.rank')); ?>

                                </th>
                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-600 dark:text-gray-300">
                                    <?php echo e(trans('admin::app.layouts.name')); ?>

                                </th>
                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-600 dark:text-gray-300">
                                    <?php echo e(trans('sales::app.performance.achievement-rate')); ?>

                                </th>
                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-600 dark:text-gray-300">
                                    <?php echo e(trans('sales::app.performance.score')); ?>

                                </th>
                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-600 dark:text-gray-300">
                                    <?php echo e(trans('admin::app.layouts.target')); ?>

                                </th>
                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-600 dark:text-gray-300">
                                    <?php echo e(trans('admin::app.layouts.achieved')); ?>

                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                v-for="(item, index) in leaderboard"
                                :key="item.id"
                                class="border-b border-gray-100 hover:bg-gray-50 dark:border-gray-800 dark:hover:bg-gray-800"
                            >
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <span
                                            class="flex h-8 w-8 items-center justify-center rounded-full text-sm font-bold"
                                            :class="{
                                                'bg-yellow-100 text-yellow-800': index === 0,
                                                'bg-gray-100 text-gray-800': index === 1,
                                                'bg-orange-100 text-orange-800': index === 2,
                                                'bg-blue-100 text-blue-800': index > 2
                                            }"
                                        >
                                            {{ index + 1 }}
                                        </span>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="font-medium text-gray-800 dark:text-white">
                                        {{ item.entity_name }}
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="mr-2 h-2 w-16 rounded-full bg-gray-200 dark:bg-gray-700">
                                            <div
                                                class="h-2 rounded-full bg-blue-600"
                                                :style="{ width: Math.min(100, item.achievement_percentage) + '%' }"
                                            ></div>
                                        </div>
                                        <span class="text-sm text-gray-600 dark:text-gray-300">
                                            {{ item.achievement_percentage }}%
                                        </span>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm font-medium text-gray-800 dark:text-white">
                                        {{ item.score }}
                                    </span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-600 dark:text-gray-300">
                                        {{ formatCurrency(item.target_amount) }}
                                    </span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-600 dark:text-gray-300">
                                        {{ formatCurrency(item.achieved_amount) }}
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-performance-filters', {
            template: '#v-performance-filters-template',

            data() {
                return {
                    filters: {
                        period: 'monthly',
                        date_from: this.getDefaultDateFrom(),
                        date_to: this.getDefaultDateTo(),
                    },
                };
            },

            methods: {
                applyFilters() {
                    this.$emitter.emit('performance-filter-updated', this.filters);
                },

                getDefaultDateFrom() {
                    const date = new Date();
                    date.setMonth(date.getMonth() - 6);
                    return date.toISOString().split('T')[0];
                },

                getDefaultDateTo() {
                    return new Date().toISOString().split('T')[0];
                },
            },

            mounted() {
                this.applyFilters();
            },
        });

        app.component('v-performance-dashboard', {
            template: '#v-performance-dashboard-template',

            data() {
                return {
                    isLoading: true,
                    summary: {},
                    targetVsActual: [],
                    trends: [],
                    leaderboard: [],
                    leaderboardType: 'individual',
                };
            },

            computed: {
                targetVsActualLabels() {
                    return this.targetVsActual.map(item => item.entity_name);
                },

                targetVsActualDatasets() {
                    return [{
                        label: 'Target',
                        data: this.targetVsActual.map(item => item.target_amount),
                        backgroundColor: '#E5E7EB',
                        barThickness: 24,
                    }, {
                        label: 'Achieved',
                        data: this.targetVsActual.map(item => item.achieved_amount),
                        backgroundColor: '#3B82F6',
                        barThickness: 24,
                    }];
                },

                trendsLabels() {
                    return this.trends.map(item => item.period_start);
                },

                trendsDatasets() {
                    return [{
                        label: 'Achievement %',
                        data: this.trends.map(item => item.avg_value),
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                    }];
                },
            },

            mounted() {
                this.loadData({});
                this.loadLeaderboard();

                this.$emitter.on('performance-filter-updated', this.loadData);
            },

            methods: {
                loadData(filters) {
                    this.isLoading = true;

                    // Load overview stats
                    this.$axios.get("<?php echo e(route('admin.sales.performance.stats')); ?>", {
                        params: { type: 'overview', ...filters }
                    }).then(response => {
                        this.summary = response.data;
                    });

                    // Load target vs actual
                    this.$axios.get("<?php echo e(route('admin.sales.performance.stats')); ?>", {
                        params: { type: 'target-vs-actual', ...filters }
                    }).then(response => {
                        this.targetVsActual = response.data.chart_data;
                    });

                    // Load trends
                    this.$axios.get("<?php echo e(route('admin.sales.performance.stats')); ?>", {
                        params: { type: 'trends', ...filters }
                    }).then(response => {
                        this.trends = response.data.trends;
                        this.isLoading = false;
                    });
                },

                loadLeaderboard() {
                    this.$axios.get("<?php echo e(route('admin.sales.performance.leaderboard')); ?>", {
                        params: { type: this.leaderboardType }
                    }).then(response => {
                        this.leaderboard = response.data.leaderboard;
                    });
                },

                formatCurrency(amount) {
                    return new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD'
                    }).format(amount);
                },
            },
        });
    </script>
<?php $__env->stopPush(); endif; ?>

<?php echo $__env->make('sales::layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm2\laravel-crm\packages\Webkul\Sales\src/resources/views/performance/index.blade.php ENDPATH**/ ?>