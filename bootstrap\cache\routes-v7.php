<?php

/*
|--------------------------------------------------------------------------
| Load The Cached Routes
|--------------------------------------------------------------------------
|
| Here we will decode and unserialize the RouteCollection instance that
| holds all of the route information for an application. This allows
| us to instantaneously load the entire route map into the router.
|
*/

app('router')->setCompiledRoutes(
    array (
  'compiled' => 
  array (
    0 => false,
    1 => 
    array (
      '/_debugbar/open' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'debugbar.openhandler',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/_debugbar/assets/stylesheets' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'debugbar.assets.css',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/_debugbar/assets/javascript' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'debugbar.assets.js',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/_debugbar/queries/explain' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'debugbar.queries.explain',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/sanctum/csrf-cookie' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'sanctum.csrf-cookie',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/_ignition/health-check' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ignition.healthCheck',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/_ignition/execute-solution' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ignition.executeSolution',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/_ignition/update-config' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'ignition.updateConfig',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/api/user' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::vr8De1AeLPIM1aJG',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'krayin.home',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'generated::uKnpxw0GF1G9y694',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/login' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.session.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.session.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/logout' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.session.destroy',
          ),
          1 => NULL,
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/forget-password' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.forgot_password.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.forgot_password.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/reset-password' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.reset_password.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/leads' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/leads/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/leads/create-by-ai' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.create_by_ai',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/leads/search' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.search',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/leads/kanban/look-up' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.kanban.look_up',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/mail/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.mail.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/groups' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.groups.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/groups/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.groups.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/types' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.types.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/types/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.types.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/roles' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.roles.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/roles/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.roles.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.roles.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/web-forms' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.web_forms.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/web-forms/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.web_forms.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.web_forms.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/workflows' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.workflows.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/workflows/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.workflows.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.workflows.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/webhooks' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.webhooks.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/webhooks/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.webhooks.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.webhooks.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/tags' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.tags.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/tags/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.tags.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/tags/search' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.tags.search',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/users' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.users.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/users/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.users.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/users/search' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.users.search',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/pipelines' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.pipelines.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/pipelines/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.pipelines.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.pipelines.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/sources' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.sources.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/sources/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.sources.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/attributes' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/attributes/check-unique-validation' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.check_unique_validation',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/attributes/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/warehouses' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.warehouses.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/warehouses/search' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.warehouses.search',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/warehouses/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.warehouses.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.warehouses.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/locations/search' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.locations.search',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/locations/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.locations.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/email-templates' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.email_templates.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/email-templates/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.email_templates.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.email_templates.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/marketing/events' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.events.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/marketing/events/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.events.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/marketing/campaigns' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.campaigns.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.campaigns.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/marketing/campaigns/events' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.campaigns.events',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/marketing/campaigns/email-templates' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.campaigns.email-templates',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/data-transfer/imports' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/settings/data-transfer/imports/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/products' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/products/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/products/search' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.search',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/contacts/persons' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.persons.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/contacts/persons/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.persons.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.persons.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/contacts/persons/search' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.persons.search',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/contacts/organizations' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.organizations.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/contacts/organizations/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.organizations.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.organizations.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/activities' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.activities.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/activities/get' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.activities.get',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/activities/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.activities.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/quotes' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.quotes.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/configuration/search' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.configuration.search',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/dashboard' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.dashboard.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/dashboard/stats' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.dashboard.stats',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/datagrid/datagrid/saved-filters' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.datagrid.saved_filters.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.datagrid.saved_filters.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/datagrid/datagrid/look-up' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.datagrid.look_up',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/tinymce/upload' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.tinymce.upload',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/account' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.user.account.edit',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/account/update' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.user.account.update',
          ),
          1 => NULL,
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/install' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'installer.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/install/api/env-file-setup' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'installer.env_file_setup',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/install/api/run-migration' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'installer.run_migration',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/install/api/run-seeder' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'installer.run_seeder',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/install/api/admin-config-setup' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'installer.admin_config_setup',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/sales/dashboard' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.dashboard.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/sales/dashboard/stats' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.dashboard.stats',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/sales/targets' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.targets.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.targets.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/sales/targets/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.targets.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/sales/performance' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.performance.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/sales/performance/stats' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.performance.stats',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/sales/performance/leaderboard' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.performance.leaderboard',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/sales/reports' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.reports.index',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.reports.store',
          ),
          1 => NULL,
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      '/admin/sales/reports/create' => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.reports.create',
          ),
          1 => NULL,
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
    ),
    2 => 
    array (
      0 => '{^(?|/_debugbar/c(?|lockwork/([^/]++)(*:39)|ache/([^/]++)(?:/([^/]++))?(*:73))|/admin/(?|reset\\-password/([^/]++)(*:115)|leads/(?|view/([^/]++)(*:145)|edit/([^/]++)(?|(*:169))|attributes/edit/([^/]++)(*:202)|stage/edit/([^/]++)(*:229)|([^/]++)(*:245)|mass\\-(?|update(*:268)|destroy(*:283))|get(?:/([^/]++))?(*:309)|product/([^/]++)(?|(*:336))|([^/]++)/(?|activities(*:367)|tags(?|(*:382))|emails(?|(*:400))|quotes(?:/([^/]++))?(*:429)))|mail(?|/(?|edit/([^/]++)(*:463)|attachment\\-download(?:/([^/]++))?(*:505))|(?:/([^/]++))?(*:528)|/(?|([^/]++)(?|/([^/]++)(*:560)|(*:568))|mass\\-(?|update(*:592)|destroy(*:607))|inbound\\-parse(*:630)|([^/]++)/tags(?|(*:654))))|s(?|ettings/(?|groups/(?|edit/([^/]++)(?|(*:706))|([^/]++)(*:723))|t(?|ypes/(?|edit(?|(?:/([^/]++))?(*:765)|/([^/]++)(*:782))|([^/]++)(*:799))|ags/(?|edit/([^/]++)(?|(*:831))|([^/]++)(*:848)|mass\\-destroy(*:869)))|roles/(?|edit/([^/]++)(?|(*:904))|([^/]++)(*:921))|w(?|eb(?|\\-forms/(?|edit(?|(?:/([^/]++))?(*:971)|/([^/]++)(*:988))|([^/]++)(*:1005))|hooks/(?|edit(?|(?:/([^/]++))?(*:1045)|/([^/]++)(*:1063))|([^/]++)(*:1081)))|orkflows/(?|edit(?|(?:/([^/]++))?(*:1125)|/([^/]++)(*:1143))|([^/]++)(*:1161))|arehouses/(?|edit/([^/]++)(*:1197)|([^/]++)/products(*:1223)|view/([^/]++)(*:1245)|edit(?:/([^/]++))?(*:1272)|([^/]++)(?|(*:1292)|/(?|tags(?|(*:1312))|activities(*:1332)))))|users/(?|edit(?|(?:/([^/]++))?(*:1375)|/([^/]++)(*:1393))|([^/]++)(*:1411)|mass\\-(?|update(*:1435)|destroy(*:1451)))|pipelines/(?|edit(?|(?:/([^/]++))?(*:1496)|/([^/]++)(*:1514))|([^/]++)(*:1532))|sources/(?|edit(?|(?:/([^/]++))?(*:1574)|/([^/]++)(*:1592))|([^/]++)(*:1610))|attributes/(?|edit/([^/]++)(?|(*:1650))|lookup(?|(?:/([^/]++))?(*:1683)|\\-entity(?:/([^/]++))?(*:1714))|([^/]++)(?|(*:1735)|/options(*:1752))|mass\\-(?|update(*:1777)|destroy(*:1793))|download(*:1811))|locations/(?|edit/([^/]++)(*:1847)|([^/]++)(*:1864))|email\\-templates/(?|edit(?|(?:/([^/]++))?(*:1915)|/([^/]++)(*:1933))|([^/]++)(*:1951))|marketing/(?|events/(?|edit(?|(?:/([^/]++))?(*:2005)|/([^/]++)(*:2023))|([^/]++)(*:2041)|mass\\-destroy(*:2063))|campaigns/(?|([^/]++)(?|(*:2097))|mass\\-destroy(*:2120)))|data\\-transfer/imports/(?|edit/([^/]++)(*:2170)|update/([^/]++)(*:2194)|d(?|estroy/([^/]++)(*:2222)|ownload(?|\\-(?|sample(?:/([^/]++))?(*:2266)|error\\-report/([^/]++)(*:2297))|/([^/]++)(*:2316)))|i(?|mport/([^/]++)(*:2345)|ndex/([^/]++)(*:2367))|validate/([^/]++)(*:2394)|sta(?|rt/([^/]++)(*:2420)|ts/([^/]++)(?:/([^/]++))?(*:2454))|link/([^/]++)(*:2477)))|ales/(?|targets/(?|([^/]++)(?|/edit(*:2523)|(*:2532))|mass\\-(?|update(*:2557)|delete(*:2572)))|performance/([^/]++)(*:2603)|reports/(?|([^/]++)(?|(*:2634)|/export(*:2650)|(*:2659))|mass\\-delete(*:2681))))|products/(?|view/([^/]++)(*:2718)|edit/([^/]++)(?|(*:2743))|([^/]++)(?|/(?|warehouses(*:2778)|inventories(?:/([^/]++))?(*:2812))|(*:2822))|mass\\-destroy(*:2845)|([^/]++)/(?|activities(*:2876)|tags(?|(*:2892))))|con(?|tacts/(?|persons/(?|view/([^/]++)(*:2943)|edit/([^/]++)(?|(*:2968))|([^/]++)(*:2986)|mass\\-destroy(*:3008)|([^/]++)/(?|tags(?|(*:3036))|activities(*:3056)))|organizations/(?|edit(?|(?:/([^/]++))?(*:3105)|/([^/]++)(*:3123))|([^/]++)(*:3141)|mass\\-destroy(*:3163)))|figuration(?|(?:/([^/]++)(?:/([^/]++))?)?(?|(*:3218))|/([^/]++)/([^/]++)/([^/]++)(*:3255)))|activities/(?|edit/([^/]++)(?|(*:3296))|download/([^/]++)(*:3323)|([^/]++)(*:3340)|mass\\-(?|update(*:3364)|destroy(*:3380)))|quotes/(?|create(?|(?:/([^/]++))?(*:3424)|(*:3433))|edit(?|(?:/([^/]++))?(*:3464)|/([^/]++)(*:3482))|print(?:/([^/]++))?(*:3511)|([^/]++)(*:3528)|search(*:3543)|mass\\-destroy(*:3565))|datagrid/datagrid/saved\\-filters/([^/]++)(?|(*:3619)))|/cache/([ \\w\\.\\/\\-\\@\\(\\)\\=]+)(*:3659)|/web\\-forms/form(?|s/([^/]++)(?|/form\\.(?|js(*:3712)|html(*:3725))|(*:3735))|/([^/]++)/form\\.html(*:3765)))/?$}sDu',
    ),
    3 => 
    array (
      39 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'debugbar.clockwork',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      73 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'debugbar.cache.delete',
            'tags' => NULL,
          ),
          1 => 
          array (
            0 => 'key',
            1 => 'tags',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      115 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.reset_password.create',
          ),
          1 => 
          array (
            0 => 'token',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      145 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.view',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      169 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.edit',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      202 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.attributes.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      229 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.stage.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      245 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      268 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.mass_update',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      283 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      309 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.get',
            'pipeline_id' => NULL,
          ),
          1 => 
          array (
            0 => 'pipeline_id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      336 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.product.remove',
          ),
          1 => 
          array (
            0 => 'lead_id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.product.add',
          ),
          1 => 
          array (
            0 => 'lead_id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      367 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.activities.index',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      382 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.tags.attach',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.tags.detach',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      400 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.emails.store',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.emails.detach',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      429 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.leads.quotes.delete',
            'quote_id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
            1 => 'quote_id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      463 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.mail.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      505 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.mail.attachment_download',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      528 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.mail.index',
            'route' => NULL,
          ),
          1 => 
          array (
            0 => 'route',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      560 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.mail.view',
          ),
          1 => 
          array (
            0 => 'route',
            1 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      568 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.mail.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      592 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.mail.mass_update',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      607 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.mail.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      630 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.mail.inbound_parse',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      654 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.mail.tags.attach',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.mail.tags.detach',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      706 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.groups.edit',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.groups.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      723 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.groups.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      765 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.types.edit',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      782 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.types.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      799 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.types.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      831 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.tags.edit',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.tags.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      848 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.tags.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      869 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.tags.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      904 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.roles.edit',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.roles.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      921 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.roles.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      971 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.web_forms.edit',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      988 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.web_forms.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1005 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.web_forms.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1045 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.webhooks.edit',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1063 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.webhooks.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1081 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.webhooks.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1125 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.workflows.edit',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1143 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.workflows.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1161 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.workflows.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1197 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.warehouses.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1223 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.warehouses.products.index',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      1245 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.warehouses.view',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1272 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.warehouses.edit',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1292 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.warehouses.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1312 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.warehouses.tags.attach',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.warehouses.tags.detach',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      1332 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.warehouse.activities.index',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      1375 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.users.edit',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1393 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.users.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1411 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.users.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1435 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.users.mass_update',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      1451 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.users.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      1496 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.pipelines.edit',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1514 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.pipelines.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1532 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.pipelines.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1574 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.sources.edit',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1592 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.sources.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1610 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.sources.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1650 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.edit',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1683 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.lookup',
            'lookup' => NULL,
          ),
          1 => 
          array (
            0 => 'lookup',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1714 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.lookup_entity',
            'lookup' => NULL,
          ),
          1 => 
          array (
            0 => 'lookup',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1735 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1752 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.options',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      1777 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.mass_update',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      1793 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      1811 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.attributes.download',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      1847 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.locations.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1864 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.locations.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1915 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.email_templates.edit',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1933 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.email_templates.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      1951 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.email_templates.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2005 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.events.edit',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2023 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.events.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2041 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.events.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2063 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.events.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      2097 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.campaigns.edit',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.campaigns.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        2 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.campaigns.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2120 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.marketing.campaigns.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      2170 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.edit',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2194 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2222 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2266 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.download_sample',
            'sample' => NULL,
          ),
          1 => 
          array (
            0 => 'sample',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2297 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.download_error_report',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2316 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.download',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2345 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.import',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2367 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.index_data',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2394 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.validate',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2420 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.start',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2454 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.stats',
            'state' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
            1 => 'state',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2477 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.data_transfer.imports.link',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2523 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.targets.edit',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      2532 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.targets.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.targets.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2557 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.targets.mass_update',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      2572 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.targets.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      2603 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.performance.view',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2634 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.reports.view',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2650 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.reports.export',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      2659 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.reports.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2681 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.sales.reports.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      2718 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.view',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2743 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.edit',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2778 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.warehouses',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      2812 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.inventories.store',
            'warehouseId' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
            1 => 'warehouseId',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2822 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2845 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      2876 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.activities.index',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      2892 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.tags.attach',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.products.tags.detach',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      2943 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.persons.view',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2968 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.persons.edit',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.persons.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      2986 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.persons.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3008 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.persons.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      3036 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.persons.tags.attach',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.persons.tags.detach',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      3056 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.persons.activities.index',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      3105 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.organizations.edit',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3123 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.organizations.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3141 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.organizations.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3163 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.contacts.organizations.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      3218 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.configuration.index',
            'slug' => NULL,
            'slug2' => NULL,
          ),
          1 => 
          array (
            0 => 'slug',
            1 => 'slug2',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.configuration.store',
            'slug' => NULL,
            'slug2' => NULL,
          ),
          1 => 
          array (
            0 => 'slug',
            1 => 'slug2',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3255 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.configuration.download',
            'slug' => NULL,
            'slug2' => NULL,
          ),
          1 => 
          array (
            0 => 'slug',
            1 => 'slug2',
            2 => 'path',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3296 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.activities.edit',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.activities.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3323 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.activities.file_download',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3340 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.activities.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3364 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.activities.mass_update',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      3380 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.activities.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      3424 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.quotes.create',
            'lead_id' => NULL,
          ),
          1 => 
          array (
            0 => 'lead_id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3433 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.quotes.store',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      3464 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.quotes.edit',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3482 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.quotes.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3511 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.quotes.print',
            'id' => NULL,
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3528 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.quotes.delete',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3543 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.quotes.search',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      3565 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.quotes.mass_delete',
          ),
          1 => 
          array (
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      3619 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.datagrid.saved_filters.update',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'PUT' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
        1 => 
        array (
          0 => 
          array (
            '_route' => 'admin.datagrid.saved_filters.destroy',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'DELETE' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3659 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'image_cache',
          ),
          1 => 
          array (
            0 => 'filename',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3712 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.web_forms.form_js',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      3725 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.web_forms.preview',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
      ),
      3735 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.web_forms.form_store',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'POST' => 0,
          ),
          3 => NULL,
          4 => false,
          5 => true,
          6 => NULL,
        ),
      ),
      3765 => 
      array (
        0 => 
        array (
          0 => 
          array (
            '_route' => 'admin.settings.web_forms.view',
          ),
          1 => 
          array (
            0 => 'id',
          ),
          2 => 
          array (
            'GET' => 0,
            'HEAD' => 1,
          ),
          3 => NULL,
          4 => false,
          5 => false,
          6 => NULL,
        ),
        1 => 
        array (
          0 => NULL,
          1 => NULL,
          2 => NULL,
          3 => NULL,
          4 => false,
          5 => false,
          6 => 0,
        ),
      ),
    ),
    4 => NULL,
  ),
  'attributes' => 
  array (
    'debugbar.openhandler' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '_debugbar/open',
      'action' => 
      array (
        'domain' => NULL,
        'middleware' => 
        array (
          0 => 'Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled',
        ),
        'uses' => 'Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@handle',
        'as' => 'debugbar.openhandler',
        'controller' => 'Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@handle',
        'namespace' => 'Barryvdh\\Debugbar\\Controllers',
        'prefix' => '_debugbar',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'debugbar.clockwork' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '_debugbar/clockwork/{id}',
      'action' => 
      array (
        'domain' => NULL,
        'middleware' => 
        array (
          0 => 'Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled',
        ),
        'uses' => 'Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@clockwork',
        'as' => 'debugbar.clockwork',
        'controller' => 'Barryvdh\\Debugbar\\Controllers\\OpenHandlerController@clockwork',
        'namespace' => 'Barryvdh\\Debugbar\\Controllers',
        'prefix' => '_debugbar',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'debugbar.assets.css' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '_debugbar/assets/stylesheets',
      'action' => 
      array (
        'domain' => NULL,
        'middleware' => 
        array (
          0 => 'Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled',
        ),
        'uses' => 'Barryvdh\\Debugbar\\Controllers\\AssetController@css',
        'as' => 'debugbar.assets.css',
        'controller' => 'Barryvdh\\Debugbar\\Controllers\\AssetController@css',
        'namespace' => 'Barryvdh\\Debugbar\\Controllers',
        'prefix' => '_debugbar',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'debugbar.assets.js' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '_debugbar/assets/javascript',
      'action' => 
      array (
        'domain' => NULL,
        'middleware' => 
        array (
          0 => 'Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled',
        ),
        'uses' => 'Barryvdh\\Debugbar\\Controllers\\AssetController@js',
        'as' => 'debugbar.assets.js',
        'controller' => 'Barryvdh\\Debugbar\\Controllers\\AssetController@js',
        'namespace' => 'Barryvdh\\Debugbar\\Controllers',
        'prefix' => '_debugbar',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'debugbar.cache.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => '_debugbar/cache/{key}/{tags?}',
      'action' => 
      array (
        'domain' => NULL,
        'middleware' => 
        array (
          0 => 'Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled',
        ),
        'uses' => 'Barryvdh\\Debugbar\\Controllers\\CacheController@delete',
        'as' => 'debugbar.cache.delete',
        'controller' => 'Barryvdh\\Debugbar\\Controllers\\CacheController@delete',
        'namespace' => 'Barryvdh\\Debugbar\\Controllers',
        'prefix' => '_debugbar',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'debugbar.queries.explain' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => '_debugbar/queries/explain',
      'action' => 
      array (
        'domain' => NULL,
        'middleware' => 
        array (
          0 => 'Barryvdh\\Debugbar\\Middleware\\DebugbarEnabled',
        ),
        'uses' => 'Barryvdh\\Debugbar\\Controllers\\QueriesController@explain',
        'as' => 'debugbar.queries.explain',
        'controller' => 'Barryvdh\\Debugbar\\Controllers\\QueriesController@explain',
        'namespace' => 'Barryvdh\\Debugbar\\Controllers',
        'prefix' => '_debugbar',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'sanctum.csrf-cookie' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'sanctum/csrf-cookie',
      'action' => 
      array (
        'uses' => 'Laravel\\Sanctum\\Http\\Controllers\\CsrfCookieController@show',
        'controller' => 'Laravel\\Sanctum\\Http\\Controllers\\CsrfCookieController@show',
        'namespace' => NULL,
        'prefix' => 'sanctum',
        'where' => 
        array (
        ),
        'middleware' => 
        array (
          0 => 'web',
        ),
        'as' => 'sanctum.csrf-cookie',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ignition.healthCheck' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '_ignition/health-check',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled',
        ),
        'uses' => 'Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController@__invoke',
        'controller' => 'Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController',
        'as' => 'ignition.healthCheck',
        'namespace' => NULL,
        'prefix' => '_ignition',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ignition.executeSolution' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => '_ignition/execute-solution',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled',
        ),
        'uses' => 'Spatie\\LaravelIgnition\\Http\\Controllers\\ExecuteSolutionController@__invoke',
        'controller' => 'Spatie\\LaravelIgnition\\Http\\Controllers\\ExecuteSolutionController',
        'as' => 'ignition.executeSolution',
        'namespace' => NULL,
        'prefix' => '_ignition',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'ignition.updateConfig' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => '_ignition/update-config',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled',
        ),
        'uses' => 'Spatie\\LaravelIgnition\\Http\\Controllers\\UpdateConfigController@__invoke',
        'controller' => 'Spatie\\LaravelIgnition\\Http\\Controllers\\UpdateConfigController',
        'as' => 'ignition.updateConfig',
        'namespace' => NULL,
        'prefix' => '_ignition',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::vr8De1AeLPIM1aJG' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'api/user',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'api',
          1 => 'auth:api',
        ),
        'uses' => 'O:55:"Laravel\\SerializableClosure\\UnsignedSerializableClosure":1:{s:12:"serializable";O:46:"Laravel\\SerializableClosure\\Serializers\\Native":5:{s:3:"use";a:0:{}s:8:"function";s:77:"function (\\Illuminate\\Http\\Request $request) {
    return $request->user();
}";s:5:"scope";s:37:"Illuminate\\Routing\\RouteFileRegistrar";s:4:"this";N;s:4:"self";s:32:"0000000000000b2f0000000000000000";}}',
        'namespace' => NULL,
        'prefix' => 'api',
        'where' => 
        array (
        ),
        'as' => 'generated::vr8De1AeLPIM1aJG',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'krayin.home' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => '/',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Controller@redirectToLogin',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Controller@redirectToLogin',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'krayin.home',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'generated::uKnpxw0GF1G9y694' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'excluded_middleware' => 
        array (
          0 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Controller@redirectToLogin',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Controller@redirectToLogin',
        'namespace' => NULL,
        'prefix' => 'admin',
        'where' => 
        array (
        ),
        'as' => 'generated::uKnpxw0GF1G9y694',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.session.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/login',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'excluded_middleware' => 
        array (
          0 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\User\\SessionController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\User\\SessionController@create',
        'namespace' => NULL,
        'prefix' => 'admin/login',
        'where' => 
        array (
        ),
        'as' => 'admin.session.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.session.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/login',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'excluded_middleware' => 
        array (
          0 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\User\\SessionController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\User\\SessionController@store',
        'namespace' => NULL,
        'prefix' => 'admin/login',
        'where' => 
        array (
        ),
        'as' => 'admin.session.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.session.destroy' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/logout',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'excluded_middleware' => 
        array (
          0 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\User\\SessionController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\User\\SessionController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin',
        'where' => 
        array (
        ),
        'as' => 'admin.session.destroy',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.forgot_password.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/forget-password',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'excluded_middleware' => 
        array (
          0 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\User\\ForgotPasswordController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\User\\ForgotPasswordController@create',
        'namespace' => NULL,
        'prefix' => 'admin/forget-password',
        'where' => 
        array (
        ),
        'as' => 'admin.forgot_password.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.forgot_password.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/forget-password',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'excluded_middleware' => 
        array (
          0 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\User\\ForgotPasswordController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\User\\ForgotPasswordController@store',
        'namespace' => NULL,
        'prefix' => 'admin/forget-password',
        'where' => 
        array (
        ),
        'as' => 'admin.forgot_password.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.reset_password.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/reset-password/{token}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'excluded_middleware' => 
        array (
          0 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\User\\ResetPasswordController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\User\\ResetPasswordController@create',
        'namespace' => NULL,
        'prefix' => 'admin/reset-password',
        'where' => 
        array (
        ),
        'as' => 'admin.reset_password.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.reset_password.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/reset-password',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'excluded_middleware' => 
        array (
          0 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\User\\ResetPasswordController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\User\\ResetPasswordController@store',
        'namespace' => NULL,
        'prefix' => 'admin/reset-password',
        'where' => 
        array (
        ),
        'as' => 'admin.reset_password.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/leads',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@index',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/leads/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@create',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/leads/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@store',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.create_by_ai' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/leads/create-by-ai',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@createByAI',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@createByAI',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.create_by_ai',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.view' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/leads/view/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@view',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@view',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.view',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/leads/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/leads/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@update',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.attributes.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/leads/attributes/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@updateAttributes',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@updateAttributes',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.attributes.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.stage.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/leads/stage/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@updateStage',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@updateStage',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.stage.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.search' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/leads/search',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@search',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@search',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.search',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/leads/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.mass_update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/leads/mass-update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@massUpdate',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@massUpdate',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.mass_update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/leads/mass-destroy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@massDestroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@massDestroy',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.mass_delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.get' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/leads/get/{pipeline_id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@get',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@get',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.get',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.product.remove' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/leads/product/{lead_id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@removeProduct',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@removeProduct',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.product.remove',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.product.add' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/leads/product/{lead_id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@addProduct',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@addProduct',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.product.add',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.kanban.look_up' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/leads/kanban/look-up',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@kanbanLookup',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\LeadController@kanbanLookup',
        'namespace' => NULL,
        'prefix' => 'admin/leads',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.kanban.look_up',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.activities.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/leads/{id}/activities',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\ActivityController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\ActivityController@index',
        'namespace' => NULL,
        'prefix' => 'admin/leads/{id}/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.activities.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.tags.attach' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/leads/{id}/tags',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\TagController@attach',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\TagController@attach',
        'namespace' => NULL,
        'prefix' => 'admin/leads/{id}/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.tags.attach',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.tags.detach' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/leads/{id}/tags',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\TagController@detach',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\TagController@detach',
        'namespace' => NULL,
        'prefix' => 'admin/leads/{id}/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.tags.detach',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.emails.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/leads/{id}/emails',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\EmailController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\EmailController@store',
        'namespace' => NULL,
        'prefix' => 'admin/leads/{id}/emails',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.emails.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.emails.detach' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/leads/{id}/emails',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\EmailController@detach',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\EmailController@detach',
        'namespace' => NULL,
        'prefix' => 'admin/leads/{id}/emails',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.emails.detach',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.leads.quotes.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/leads/{id}/quotes/{quote_id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\QuoteController@delete',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Lead\\QuoteController@delete',
        'namespace' => NULL,
        'prefix' => 'admin/leads/{id}/quotes',
        'where' => 
        array (
        ),
        'as' => 'admin.leads.quotes.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.mail.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/mail/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@store',
        'namespace' => NULL,
        'prefix' => 'admin/mail',
        'where' => 
        array (
        ),
        'as' => 'admin.mail.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.mail.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/mail/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@update',
        'namespace' => NULL,
        'prefix' => 'admin/mail',
        'where' => 
        array (
        ),
        'as' => 'admin.mail.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.mail.attachment_download' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/mail/attachment-download/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@download',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@download',
        'namespace' => NULL,
        'prefix' => 'admin/mail',
        'where' => 
        array (
        ),
        'as' => 'admin.mail.attachment_download',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.mail.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/mail/{route?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@index',
        'namespace' => NULL,
        'prefix' => 'admin/mail',
        'where' => 
        array (
        ),
        'as' => 'admin.mail.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.mail.view' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/mail/{route}/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@view',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@view',
        'namespace' => NULL,
        'prefix' => 'admin/mail',
        'where' => 
        array (
        ),
        'as' => 'admin.mail.view',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.mail.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/mail/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/mail',
        'where' => 
        array (
        ),
        'as' => 'admin.mail.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.mail.mass_update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/mail/mass-update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@massUpdate',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@massUpdate',
        'namespace' => NULL,
        'prefix' => 'admin/mail',
        'where' => 
        array (
        ),
        'as' => 'admin.mail.mass_update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.mail.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/mail/mass-destroy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@massDestroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@massDestroy',
        'namespace' => NULL,
        'prefix' => 'admin/mail',
        'where' => 
        array (
        ),
        'as' => 'admin.mail.mass_delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.mail.inbound_parse' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/mail/inbound-parse',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@inboundParse',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\EmailController@inboundParse',
        'namespace' => NULL,
        'prefix' => 'admin/mail',
        'where' => 
        array (
        ),
        'as' => 'admin.mail.inbound_parse',
        'excluded_middleware' => 
        array (
          0 => 'user',
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.mail.tags.attach' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/mail/{id}/tags',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\TagController@attach',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\TagController@attach',
        'namespace' => NULL,
        'prefix' => 'admin/mail/{id}/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.mail.tags.attach',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.mail.tags.detach' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/mail/{id}/tags',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\TagController@detach',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Mail\\TagController@detach',
        'namespace' => NULL,
        'prefix' => 'admin/mail/{id}/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.mail.tags.detach',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\SettingController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\SettingController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.groups.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/groups',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\GroupController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\GroupController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/groups',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.groups.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.groups.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/groups/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\GroupController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\GroupController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/groups',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.groups.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.groups.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/groups/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\GroupController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\GroupController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/groups',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.groups.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.groups.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/groups/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\GroupController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\GroupController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/groups',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.groups.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.groups.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/groups/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\GroupController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\GroupController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/groups',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.groups.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.types.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/types',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TypeController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TypeController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/types',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.types.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.types.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/types/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TypeController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TypeController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/types',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.types.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.types.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/types/edit/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TypeController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TypeController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/types',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.types.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.types.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/types/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TypeController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TypeController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/types',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.types.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.types.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/types/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TypeController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TypeController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/types',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.types.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.roles.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/roles',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\RoleController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\RoleController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/roles',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.roles.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.roles.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/roles/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\RoleController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\RoleController@create',
        'namespace' => NULL,
        'prefix' => 'admin/settings/roles',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.roles.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.roles.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/roles/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\RoleController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\RoleController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/roles',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.roles.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.roles.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/roles/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\RoleController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\RoleController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/roles',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.roles.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.roles.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/roles/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\RoleController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\RoleController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/roles',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.roles.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.roles.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/roles/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\RoleController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\RoleController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/roles',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.roles.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.web_forms.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/web-forms',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebFormController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebFormController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/web-forms',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.web_forms.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.web_forms.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/web-forms/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebFormController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebFormController@create',
        'namespace' => NULL,
        'prefix' => 'admin/settings/web-forms',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.web_forms.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.web_forms.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/web-forms/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebFormController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebFormController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/web-forms',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.web_forms.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.web_forms.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/web-forms/edit/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebFormController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebFormController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/web-forms',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.web_forms.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.web_forms.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/web-forms/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebFormController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebFormController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/web-forms',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.web_forms.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.web_forms.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/web-forms/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebFormController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebFormController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/web-forms',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.web_forms.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.workflows.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/workflows',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WorkflowController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WorkflowController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/workflows',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.workflows.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.workflows.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/workflows/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WorkflowController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WorkflowController@create',
        'namespace' => NULL,
        'prefix' => 'admin/settings/workflows',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.workflows.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.workflows.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/workflows/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WorkflowController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WorkflowController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/workflows',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.workflows.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.workflows.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/workflows/edit/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WorkflowController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WorkflowController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/workflows',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.workflows.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.workflows.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/workflows/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WorkflowController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WorkflowController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/workflows',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.workflows.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.workflows.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/workflows/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WorkflowController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WorkflowController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/workflows',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.workflows.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.webhooks.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/webhooks',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebhookController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebhookController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/webhooks',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.webhooks.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.webhooks.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/webhooks/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebhookController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebhookController@create',
        'namespace' => NULL,
        'prefix' => 'admin/settings/webhooks',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.webhooks.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.webhooks.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/webhooks/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebhookController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebhookController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/webhooks',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.webhooks.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.webhooks.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/webhooks/edit/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebhookController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebhookController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/webhooks',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.webhooks.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.webhooks.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/webhooks/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebhookController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebhookController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/webhooks',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.webhooks.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.webhooks.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/webhooks/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebhookController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\WebhookController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/webhooks',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.webhooks.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.tags.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/tags',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.tags.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.tags.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/tags/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.tags.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.tags.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/tags/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.tags.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.tags.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/tags/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.tags.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.tags.search' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/tags/search',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@search',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@search',
        'namespace' => NULL,
        'prefix' => 'admin/settings/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.tags.search',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.tags.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/tags/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.tags.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.tags.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/tags/mass-destroy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@massDestroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\TagController@massDestroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.tags.mass_delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.users.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/users',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/users',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.users.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.users.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/users/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/users',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.users.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.users.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/users/edit/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/users',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.users.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.users.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/users/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/users',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.users.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.users.search' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/users/search',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@search',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@search',
        'namespace' => NULL,
        'prefix' => 'admin/settings/users',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.users.search',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.users.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/users/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/users',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.users.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.users.mass_update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/users/mass-update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@massUpdate',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@massUpdate',
        'namespace' => NULL,
        'prefix' => 'admin/settings/users',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.users.mass_update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.users.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/users/mass-destroy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@massDestroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\UserController@massDestroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/users',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.users.mass_delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.pipelines.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/pipelines',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\PipelineController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\PipelineController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/pipelines',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.pipelines.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.pipelines.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/pipelines/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\PipelineController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\PipelineController@create',
        'namespace' => NULL,
        'prefix' => 'admin/settings/pipelines',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.pipelines.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.pipelines.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/pipelines/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\PipelineController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\PipelineController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/pipelines',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.pipelines.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.pipelines.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/pipelines/edit/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\PipelineController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\PipelineController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/pipelines',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.pipelines.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.pipelines.update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/pipelines/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\PipelineController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\PipelineController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/pipelines',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.pipelines.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.pipelines.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/pipelines/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\PipelineController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\PipelineController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/pipelines',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.pipelines.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.sources.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/sources',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\SourceController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\SourceController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/sources',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.sources.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.sources.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/sources/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\SourceController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\SourceController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/sources',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.sources.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.sources.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/sources/edit/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\SourceController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\SourceController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/sources',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.sources.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.sources.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/sources/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\SourceController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\SourceController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/sources',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.sources.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.sources.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/sources/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\SourceController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\SourceController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/sources',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.sources.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/attributes',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.check_unique_validation' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/attributes/check-unique-validation',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@checkUniqueValidation',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@checkUniqueValidation',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.check_unique_validation',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/attributes/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@create',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/attributes/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/attributes/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/attributes/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.lookup' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/attributes/lookup/{lookup?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@lookup',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@lookup',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.lookup',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.lookup_entity' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/attributes/lookup-entity/{lookup?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@lookupEntity',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@lookupEntity',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.lookup_entity',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/attributes/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.options' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/attributes/{id}/options',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@getAttributeOptions',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@getAttributeOptions',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.options',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.mass_update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/attributes/mass-update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@massUpdate',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@massUpdate',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.mass_update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/attributes/mass-destroy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@massDestroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@massDestroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.mass_delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.attributes.download' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/attributes/download',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@download',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\AttributeController@download',
        'namespace' => NULL,
        'prefix' => 'admin/settings/attributes',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.attributes.download',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.warehouses.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/warehouses/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/warehouses',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.warehouses.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.warehouses.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/warehouses',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/warehouses',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.warehouses.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.warehouses.search' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/warehouses/search',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@search',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@search',
        'namespace' => NULL,
        'prefix' => 'admin/settings/warehouses',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.warehouses.search',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.warehouses.products.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/warehouses/{id}/products',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@products',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@products',
        'namespace' => NULL,
        'prefix' => 'admin/settings/warehouses',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.warehouses.products.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.warehouses.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/warehouses/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@create',
        'namespace' => NULL,
        'prefix' => 'admin/settings/warehouses',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.warehouses.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.warehouses.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/warehouses/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/warehouses',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.warehouses.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.warehouses.view' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/warehouses/view/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@view',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@view',
        'namespace' => NULL,
        'prefix' => 'admin/settings/warehouses',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.warehouses.view',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.warehouses.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/warehouses/edit/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/warehouses',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.warehouses.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.warehouses.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/warehouses/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\WarehouseController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/warehouses',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.warehouses.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.warehouses.tags.attach' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/warehouses/{id}/tags',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\TagController@attach',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\TagController@attach',
        'namespace' => NULL,
        'prefix' => 'admin/settings/warehouses/{id}/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.warehouses.tags.attach',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.warehouses.tags.detach' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/warehouses/{id}/tags',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\TagController@detach',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\TagController@detach',
        'namespace' => NULL,
        'prefix' => 'admin/settings/warehouses/{id}/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.warehouses.tags.detach',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.warehouse.activities.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/warehouses/{id}/activities',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\ActivityController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Warehouse\\ActivityController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/warehouses/{id}/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.warehouse.activities.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.locations.search' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/locations/search',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\LocationController@search',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\LocationController@search',
        'namespace' => NULL,
        'prefix' => 'admin/settings/locations',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.locations.search',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.locations.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/locations/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\LocationController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\LocationController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/locations',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.locations.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.locations.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/locations/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\LocationController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\LocationController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/locations',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.locations.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.locations.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/locations/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\LocationController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\LocationController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/locations',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.locations.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.email_templates.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/email-templates',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\EmailTemplateController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\EmailTemplateController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/email-templates',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.email_templates.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.email_templates.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/email-templates/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\EmailTemplateController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\EmailTemplateController@create',
        'namespace' => NULL,
        'prefix' => 'admin/settings/email-templates',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.email_templates.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.email_templates.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/email-templates/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\EmailTemplateController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\EmailTemplateController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/email-templates',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.email_templates.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.email_templates.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/email-templates/edit/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\EmailTemplateController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\EmailTemplateController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/email-templates',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.email_templates.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.email_templates.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/email-templates/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\EmailTemplateController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\EmailTemplateController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/email-templates',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.email_templates.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.email_templates.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/email-templates/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\EmailTemplateController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\EmailTemplateController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/email-templates',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.email_templates.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.events.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/marketing/events',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\EventController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\EventController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/events',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.events.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.events.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/marketing/events/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\EventController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\EventController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/events',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.events.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.events.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/marketing/events/edit/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\EventController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\EventController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/events',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.events.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.events.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/marketing/events/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\EventController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\EventController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/events',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.events.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.events.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/marketing/events/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\EventController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\EventController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/events',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.events.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.events.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/marketing/events/mass-destroy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\EventController@massDestroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\EventController@massDestroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/events',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.events.mass_delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.campaigns.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/marketing/campaigns',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/campaigns',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.campaigns.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.campaigns.events' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/marketing/campaigns/events',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@getEvents',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@getEvents',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/campaigns',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.campaigns.events',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.campaigns.email-templates' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/marketing/campaigns/email-templates',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@getEmailTemplates',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@getEmailTemplates',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/campaigns',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.campaigns.email-templates',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.campaigns.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/marketing/campaigns',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/campaigns',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.campaigns.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.campaigns.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/marketing/campaigns/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@show',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@show',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/campaigns',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.campaigns.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.campaigns.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/marketing/campaigns/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/campaigns',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.campaigns.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.campaigns.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/marketing/campaigns/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/campaigns',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.campaigns.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.marketing.campaigns.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/marketing/campaigns/mass-destroy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@massDestroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\Marketing\\CampaignsController@massDestroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/marketing/campaigns',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.marketing.campaigns.mass_delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/data-transfer/imports',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@index',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/data-transfer/imports/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@create',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/settings/data-transfer/imports/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@store',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/data-transfer/imports/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/settings/data-transfer/imports/update/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@update',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/settings/data-transfer/imports/destroy/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.import' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/data-transfer/imports/import/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@import',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@import',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.import',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.validate' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/data-transfer/imports/validate/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@validateImport',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@validateImport',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.validate',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.start' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/data-transfer/imports/start/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@start',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@start',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.start',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.link' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/data-transfer/imports/link/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@link',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@link',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.link',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.index_data' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/data-transfer/imports/index/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@indexData',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@indexData',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.index_data',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.stats' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/data-transfer/imports/stats/{id}/{state?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@stats',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@stats',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.stats',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.download_sample' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/data-transfer/imports/download-sample/{sample?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@downloadSample',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@downloadSample',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.download_sample',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.download' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/data-transfer/imports/download/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@download',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@download',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.download',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.data_transfer.imports.download_error_report' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/settings/data-transfer/imports/download-error-report/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@downloadErrorReport',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Settings\\DataTransfer\\ImportController@downloadErrorReport',
        'namespace' => NULL,
        'prefix' => 'admin/settings/data-transfer/imports',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.data_transfer.imports.download_error_report',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/products',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@index',
        'namespace' => NULL,
        'prefix' => 'admin/products',
        'where' => 
        array (
        ),
        'as' => 'admin.products.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/products/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@create',
        'namespace' => NULL,
        'prefix' => 'admin/products',
        'where' => 
        array (
        ),
        'as' => 'admin.products.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/products/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@store',
        'namespace' => NULL,
        'prefix' => 'admin/products',
        'where' => 
        array (
        ),
        'as' => 'admin.products.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.view' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/products/view/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@view',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@view',
        'namespace' => NULL,
        'prefix' => 'admin/products',
        'where' => 
        array (
        ),
        'as' => 'admin.products.view',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/products/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/products',
        'where' => 
        array (
        ),
        'as' => 'admin.products.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/products/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@update',
        'namespace' => NULL,
        'prefix' => 'admin/products',
        'where' => 
        array (
        ),
        'as' => 'admin.products.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.search' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/products/search',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@search',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@search',
        'namespace' => NULL,
        'prefix' => 'admin/products',
        'where' => 
        array (
        ),
        'as' => 'admin.products.search',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.warehouses' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/products/{id}/warehouses',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@warehouses',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@warehouses',
        'namespace' => NULL,
        'prefix' => 'admin/products',
        'where' => 
        array (
        ),
        'as' => 'admin.products.warehouses',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.inventories.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/products/{id}/inventories/{warehouseId?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@storeInventories',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@storeInventories',
        'namespace' => NULL,
        'prefix' => 'admin/products',
        'where' => 
        array (
        ),
        'as' => 'admin.products.inventories.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/products/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/products',
        'where' => 
        array (
        ),
        'as' => 'admin.products.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/products/mass-destroy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@massDestroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ProductController@massDestroy',
        'namespace' => NULL,
        'prefix' => 'admin/products',
        'where' => 
        array (
        ),
        'as' => 'admin.products.mass_delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.activities.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/products/{id}/activities',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ActivityController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\ActivityController@index',
        'namespace' => NULL,
        'prefix' => 'admin/products/{id}/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.products.activities.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.tags.attach' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/products/{id}/tags',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\TagController@attach',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\TagController@attach',
        'namespace' => NULL,
        'prefix' => 'admin/products/{id}/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.products.tags.attach',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.products.tags.detach' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/products/{id}/tags',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Products\\TagController@detach',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Products\\TagController@detach',
        'namespace' => NULL,
        'prefix' => 'admin/products/{id}/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.products.tags.detach',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.persons.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/contacts/persons',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@index',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/persons',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.persons.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.persons.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/contacts/persons/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@create',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/persons',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.persons.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.persons.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/contacts/persons/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@store',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/persons',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.persons.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.persons.view' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/contacts/persons/view/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@show',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@show',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/persons',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.persons.view',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.persons.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/contacts/persons/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/persons',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.persons.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.persons.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/contacts/persons/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@update',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/persons',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.persons.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.persons.search' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/contacts/persons/search',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@search',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@search',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/persons',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.persons.search',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.persons.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/contacts/persons/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
          3 => 'throttle:100,60',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/persons',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.persons.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.persons.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/contacts/persons/mass-destroy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@massDestroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\PersonController@massDestroy',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/persons',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.persons.mass_delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.persons.tags.attach' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/contacts/persons/{id}/tags',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\TagController@attach',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\TagController@attach',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/persons/{id}/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.persons.tags.attach',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.persons.tags.detach' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/contacts/persons/{id}/tags',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\TagController@detach',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\TagController@detach',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/persons/{id}/tags',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.persons.tags.detach',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.persons.activities.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/contacts/persons/{id}/activities',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\ActivityController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\Persons\\ActivityController@index',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/persons/{id}/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.persons.activities.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.organizations.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/contacts/organizations',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@index',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/organizations',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.organizations.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.organizations.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/contacts/organizations/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@create',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/organizations',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.organizations.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.organizations.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/contacts/organizations/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@store',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/organizations',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.organizations.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.organizations.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/contacts/organizations/edit/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/organizations',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.organizations.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.organizations.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/contacts/organizations/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@update',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/organizations',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.organizations.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.organizations.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/contacts/organizations/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/organizations',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.organizations.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.contacts.organizations.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/contacts/organizations/mass-destroy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@massDestroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Contact\\OrganizationController@massDestroy',
        'namespace' => NULL,
        'prefix' => 'admin/contacts/organizations',
        'where' => 
        array (
        ),
        'as' => 'admin.contacts.organizations.mass_delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.activities.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/activities',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@index',
        'namespace' => NULL,
        'prefix' => 'admin/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.activities.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.activities.get' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/activities/get',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@get',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@get',
        'namespace' => NULL,
        'prefix' => 'admin/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.activities.get',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.activities.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/activities/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@store',
        'namespace' => NULL,
        'prefix' => 'admin/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.activities.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.activities.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/activities/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.activities.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.activities.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/activities/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@update',
        'namespace' => NULL,
        'prefix' => 'admin/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.activities.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.activities.file_download' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/activities/download/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@download',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@download',
        'namespace' => NULL,
        'prefix' => 'admin/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.activities.file_download',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.activities.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/activities/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.activities.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.activities.mass_update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/activities/mass-update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@massUpdate',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@massUpdate',
        'namespace' => NULL,
        'prefix' => 'admin/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.activities.mass_update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.activities.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/activities/mass-destroy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@massDestroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Activity\\ActivityController@massDestroy',
        'namespace' => NULL,
        'prefix' => 'admin/activities',
        'where' => 
        array (
        ),
        'as' => 'admin.activities.mass_delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.quotes.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/quotes',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@index',
        'namespace' => NULL,
        'prefix' => 'admin/quotes',
        'where' => 
        array (
        ),
        'as' => 'admin.quotes.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.quotes.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/quotes/create/{lead_id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@create',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@create',
        'namespace' => NULL,
        'prefix' => 'admin/quotes',
        'where' => 
        array (
        ),
        'as' => 'admin.quotes.create',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.quotes.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/quotes/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@store',
        'namespace' => NULL,
        'prefix' => 'admin/quotes',
        'where' => 
        array (
        ),
        'as' => 'admin.quotes.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.quotes.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/quotes/edit/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/quotes',
        'where' => 
        array (
        ),
        'as' => 'admin.quotes.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.quotes.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/quotes/edit/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@update',
        'namespace' => NULL,
        'prefix' => 'admin/quotes',
        'where' => 
        array (
        ),
        'as' => 'admin.quotes.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.quotes.print' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/quotes/print/{id?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@print',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@print',
        'namespace' => NULL,
        'prefix' => 'admin/quotes',
        'where' => 
        array (
        ),
        'as' => 'admin.quotes.print',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.quotes.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/quotes/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/quotes',
        'where' => 
        array (
        ),
        'as' => 'admin.quotes.delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.quotes.search' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/quotes/search',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@search',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@search',
        'namespace' => NULL,
        'prefix' => 'admin/quotes',
        'where' => 
        array (
        ),
        'as' => 'admin.quotes.search',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.quotes.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/quotes/mass-destroy',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@massDestroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Quote\\QuoteController@massDestroy',
        'namespace' => NULL,
        'prefix' => 'admin/quotes',
        'where' => 
        array (
        ),
        'as' => 'admin.quotes.mass_delete',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.configuration.search' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/configuration/search',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Configuration\\ConfigurationController@search',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Configuration\\ConfigurationController@search',
        'namespace' => NULL,
        'prefix' => 'admin/configuration',
        'where' => 
        array (
        ),
        'as' => 'admin.configuration.search',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.configuration.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/configuration/{slug?}/{slug2?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Configuration\\ConfigurationController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Configuration\\ConfigurationController@index',
        'namespace' => NULL,
        'prefix' => 'admin/configuration/{slug?}/{slug2?}',
        'where' => 
        array (
        ),
        'as' => 'admin.configuration.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.configuration.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/configuration/{slug?}/{slug2?}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Configuration\\ConfigurationController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Configuration\\ConfigurationController@store',
        'namespace' => NULL,
        'prefix' => 'admin/configuration/{slug?}/{slug2?}',
        'where' => 
        array (
        ),
        'as' => 'admin.configuration.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.configuration.download' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/configuration/{slug?}/{slug2?}/{path}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\Configuration\\ConfigurationController@download',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\Configuration\\ConfigurationController@download',
        'namespace' => NULL,
        'prefix' => 'admin/configuration/{slug?}/{slug2?}',
        'where' => 
        array (
        ),
        'as' => 'admin.configuration.download',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.dashboard.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/dashboard',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\DashboardController@index',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\DashboardController@index',
        'namespace' => NULL,
        'prefix' => 'admin/dashboard',
        'where' => 
        array (
        ),
        'as' => 'admin.dashboard.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.dashboard.stats' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/dashboard/stats',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\DashboardController@stats',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\DashboardController@stats',
        'namespace' => NULL,
        'prefix' => 'admin/dashboard',
        'where' => 
        array (
        ),
        'as' => 'admin.dashboard.stats',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.datagrid.saved_filters.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/datagrid/datagrid/saved-filters',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\DataGrid\\SavedFilterController@store',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\DataGrid\\SavedFilterController@store',
        'namespace' => NULL,
        'prefix' => 'admin/datagrid/datagrid/saved-filters',
        'where' => 
        array (
        ),
        'as' => 'admin.datagrid.saved_filters.store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.datagrid.saved_filters.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/datagrid/datagrid/saved-filters',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\DataGrid\\SavedFilterController@get',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\DataGrid\\SavedFilterController@get',
        'namespace' => NULL,
        'prefix' => 'admin/datagrid/datagrid/saved-filters',
        'where' => 
        array (
        ),
        'as' => 'admin.datagrid.saved_filters.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.datagrid.saved_filters.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/datagrid/datagrid/saved-filters/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\DataGrid\\SavedFilterController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\DataGrid\\SavedFilterController@update',
        'namespace' => NULL,
        'prefix' => 'admin/datagrid/datagrid/saved-filters',
        'where' => 
        array (
        ),
        'as' => 'admin.datagrid.saved_filters.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.datagrid.saved_filters.destroy' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/datagrid/datagrid/saved-filters/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\DataGrid\\SavedFilterController@destroy',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\DataGrid\\SavedFilterController@destroy',
        'namespace' => NULL,
        'prefix' => 'admin/datagrid/datagrid/saved-filters',
        'where' => 
        array (
        ),
        'as' => 'admin.datagrid.saved_filters.destroy',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.datagrid.look_up' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/datagrid/datagrid/look-up',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\DataGridController@lookUp',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\DataGridController@lookUp',
        'namespace' => NULL,
        'prefix' => 'admin/datagrid',
        'where' => 
        array (
        ),
        'as' => 'admin.datagrid.look_up',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.tinymce.upload' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/tinymce/upload',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\TinyMCEController@upload',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\TinyMCEController@upload',
        'namespace' => NULL,
        'prefix' => 'admin',
        'where' => 
        array (
        ),
        'as' => 'admin.tinymce.upload',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.user.account.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/account',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\User\\AccountController@edit',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\User\\AccountController@edit',
        'namespace' => NULL,
        'prefix' => 'admin/account',
        'where' => 
        array (
        ),
        'as' => 'admin.user.account.edit',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.user.account.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/account/update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Admin\\Http\\Controllers\\User\\AccountController@update',
        'controller' => 'Webkul\\Admin\\Http\\Controllers\\User\\AccountController@update',
        'namespace' => NULL,
        'prefix' => 'admin/account',
        'where' => 
        array (
        ),
        'as' => 'admin.user.account.update',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'installer.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'install',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'installer_locale',
        ),
        'uses' => 'Webkul\\Installer\\Http\\Controllers\\InstallerController@index',
        'controller' => 'Webkul\\Installer\\Http\\Controllers\\InstallerController@index',
        'namespace' => NULL,
        'prefix' => '',
        'where' => 
        array (
        ),
        'as' => 'installer.index',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'installer.env_file_setup' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'install/api/env-file-setup',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'installer_locale',
          2 => 'Illuminate\\Session\\Middleware\\StartSession',
        ),
        'uses' => 'Webkul\\Installer\\Http\\Controllers\\InstallerController@envFileSetup',
        'controller' => 'Webkul\\Installer\\Http\\Controllers\\InstallerController@envFileSetup',
        'namespace' => NULL,
        'prefix' => '/install/api',
        'where' => 
        array (
        ),
        'as' => 'installer.env_file_setup',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'installer.run_migration' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'install/api/run-migration',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'installer_locale',
          2 => 'Illuminate\\Session\\Middleware\\StartSession',
        ),
        'excluded_middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'Webkul\\Installer\\Http\\Controllers\\InstallerController@runMigration',
        'controller' => 'Webkul\\Installer\\Http\\Controllers\\InstallerController@runMigration',
        'namespace' => NULL,
        'prefix' => '/install/api',
        'where' => 
        array (
        ),
        'as' => 'installer.run_migration',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'installer.run_seeder' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'install/api/run-seeder',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'installer_locale',
          2 => 'Illuminate\\Session\\Middleware\\StartSession',
        ),
        'excluded_middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'Webkul\\Installer\\Http\\Controllers\\InstallerController@runSeeder',
        'controller' => 'Webkul\\Installer\\Http\\Controllers\\InstallerController@runSeeder',
        'namespace' => NULL,
        'prefix' => '/install/api',
        'where' => 
        array (
        ),
        'as' => 'installer.run_seeder',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'installer.admin_config_setup' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'install/api/admin-config-setup',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'installer_locale',
          2 => 'Illuminate\\Session\\Middleware\\StartSession',
        ),
        'excluded_middleware' => 
        array (
          0 => 'web',
        ),
        'uses' => 'Webkul\\Installer\\Http\\Controllers\\InstallerController@adminConfigSetup',
        'controller' => 'Webkul\\Installer\\Http\\Controllers\\InstallerController@adminConfigSetup',
        'namespace' => NULL,
        'prefix' => '/install/api',
        'where' => 
        array (
        ),
        'as' => 'installer.admin_config_setup',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'image_cache' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'cache/{filename}',
      'action' => 
      array (
        'uses' => 'Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage',
        'as' => 'image_cache',
        'controller' => 'Webkul\\Installer\\Http\\Controllers\\ImageCacheController@getImage',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
        'filename' => '[ \\w\\.\\/\\-\\@\\(\\)\\=]+',
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.web_forms.form_js' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'web-forms/forms/{id}/form.js',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
        ),
        'uses' => 'Webkul\\WebForm\\Http\\Controllers\\WebFormController@formJS',
        'controller' => 'Webkul\\WebForm\\Http\\Controllers\\WebFormController@formJS',
        'namespace' => NULL,
        'prefix' => 'web-forms',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.web_forms.form_js',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.web_forms.preview' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'web-forms/forms/{id}/form.html',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
        ),
        'uses' => 'Webkul\\WebForm\\Http\\Controllers\\WebFormController@preview',
        'controller' => 'Webkul\\WebForm\\Http\\Controllers\\WebFormController@preview',
        'namespace' => NULL,
        'prefix' => 'web-forms',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.web_forms.preview',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.web_forms.form_store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'web-forms/forms/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
        ),
        'uses' => 'Webkul\\WebForm\\Http\\Controllers\\WebFormController@formStore',
        'controller' => 'Webkul\\WebForm\\Http\\Controllers\\WebFormController@formStore',
        'namespace' => NULL,
        'prefix' => 'web-forms',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.web_forms.form_store',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.settings.web_forms.view' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'web-forms/form/{id}/form.html',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\WebForm\\Http\\Controllers\\WebFormController@view',
        'controller' => 'Webkul\\WebForm\\Http\\Controllers\\WebFormController@view',
        'namespace' => NULL,
        'prefix' => 'web-forms',
        'where' => 
        array (
        ),
        'as' => 'admin.settings.web_forms.view',
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.dashboard.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/dashboard',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\DashboardController@index',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\DashboardController@index',
        'as' => 'admin.sales.dashboard.index',
        'namespace' => NULL,
        'prefix' => 'admin/sales',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.dashboard.stats' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/dashboard/stats',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\DashboardController@stats',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\DashboardController@stats',
        'as' => 'admin.sales.dashboard.stats',
        'namespace' => NULL,
        'prefix' => 'admin/sales',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.targets.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/targets',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@index',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@index',
        'as' => 'admin.sales.targets.index',
        'namespace' => NULL,
        'prefix' => 'admin/sales/targets',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.targets.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/targets/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@create',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@create',
        'as' => 'admin.sales.targets.create',
        'namespace' => NULL,
        'prefix' => 'admin/sales/targets',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.targets.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/sales/targets',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@store',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@store',
        'as' => 'admin.sales.targets.store',
        'namespace' => NULL,
        'prefix' => 'admin/sales/targets',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.targets.edit' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/targets/{id}/edit',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@edit',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@edit',
        'as' => 'admin.sales.targets.edit',
        'namespace' => NULL,
        'prefix' => 'admin/sales/targets',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.targets.update' => 
    array (
      'methods' => 
      array (
        0 => 'PUT',
      ),
      'uri' => 'admin/sales/targets/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@update',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@update',
        'as' => 'admin.sales.targets.update',
        'namespace' => NULL,
        'prefix' => 'admin/sales/targets',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.targets.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/sales/targets/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@destroy',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@destroy',
        'as' => 'admin.sales.targets.delete',
        'namespace' => NULL,
        'prefix' => 'admin/sales/targets',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.targets.mass_update' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/sales/targets/mass-update',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@massUpdate',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@massUpdate',
        'as' => 'admin.sales.targets.mass_update',
        'namespace' => NULL,
        'prefix' => 'admin/sales/targets',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.targets.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/sales/targets/mass-delete',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@massDestroy',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\TargetController@massDestroy',
        'as' => 'admin.sales.targets.mass_delete',
        'namespace' => NULL,
        'prefix' => 'admin/sales/targets',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.performance.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/performance',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\PerformanceController@index',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\PerformanceController@index',
        'as' => 'admin.sales.performance.index',
        'namespace' => NULL,
        'prefix' => 'admin/sales/performance',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.performance.stats' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/performance/stats',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\PerformanceController@stats',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\PerformanceController@stats',
        'as' => 'admin.sales.performance.stats',
        'namespace' => NULL,
        'prefix' => 'admin/sales/performance',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.performance.leaderboard' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/performance/leaderboard',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\PerformanceController@leaderboard',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\PerformanceController@leaderboard',
        'as' => 'admin.sales.performance.leaderboard',
        'namespace' => NULL,
        'prefix' => 'admin/sales/performance',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.performance.view' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/performance/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\PerformanceController@view',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\PerformanceController@view',
        'as' => 'admin.sales.performance.view',
        'namespace' => NULL,
        'prefix' => 'admin/sales/performance',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.reports.index' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/reports',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@index',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@index',
        'as' => 'admin.sales.reports.index',
        'namespace' => NULL,
        'prefix' => 'admin/sales/reports',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.reports.create' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/reports/create',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@create',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@create',
        'as' => 'admin.sales.reports.create',
        'namespace' => NULL,
        'prefix' => 'admin/sales/reports',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.reports.store' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/sales/reports',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@store',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@store',
        'as' => 'admin.sales.reports.store',
        'namespace' => NULL,
        'prefix' => 'admin/sales/reports',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.reports.view' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/reports/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@view',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@view',
        'as' => 'admin.sales.reports.view',
        'namespace' => NULL,
        'prefix' => 'admin/sales/reports',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.reports.export' => 
    array (
      'methods' => 
      array (
        0 => 'GET',
        1 => 'HEAD',
      ),
      'uri' => 'admin/sales/reports/{id}/export',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@export',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@export',
        'as' => 'admin.sales.reports.export',
        'namespace' => NULL,
        'prefix' => 'admin/sales/reports',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.reports.delete' => 
    array (
      'methods' => 
      array (
        0 => 'DELETE',
      ),
      'uri' => 'admin/sales/reports/{id}',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@destroy',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@destroy',
        'as' => 'admin.sales.reports.delete',
        'namespace' => NULL,
        'prefix' => 'admin/sales/reports',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
    'admin.sales.reports.mass_delete' => 
    array (
      'methods' => 
      array (
        0 => 'POST',
      ),
      'uri' => 'admin/sales/reports/mass-delete',
      'action' => 
      array (
        'middleware' => 
        array (
          0 => 'web',
          1 => 'admin_locale',
          2 => 'user',
        ),
        'uses' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@massDestroy',
        'controller' => 'Webkul\\Sales\\Http\\Controllers\\ReportController@massDestroy',
        'as' => 'admin.sales.reports.mass_delete',
        'namespace' => NULL,
        'prefix' => 'admin/sales/reports',
        'where' => 
        array (
        ),
      ),
      'fallback' => false,
      'defaults' => 
      array (
      ),
      'wheres' => 
      array (
      ),
      'bindingFields' => 
      array (
      ),
      'lockSeconds' => NULL,
      'waitSeconds' => NULL,
      'withTrashed' => false,
    ),
  ),
)
);
