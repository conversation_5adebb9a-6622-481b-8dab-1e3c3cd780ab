<?php $__env->startSection('title'); ?>
    <?php echo e(trans('sales::app.dashboard.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="flex flex-col gap-4">
        <?php echo view_render_event('sales.dashboard.index.header.before'); ?>


        <!-- Page Header -->
        <div class="flex items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300">
            <div class="flex flex-col gap-2">
                <div class="flex cursor-pointer items-center">
                    <?php if (isset($component)) { $__componentOriginal477735b45b070062c5df1d72c43d48f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal477735b45b070062c5df1d72c43d48f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.breadcrumbs.index','data' => ['name' => 'sales.dashboard']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::breadcrumbs'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'sales.dashboard']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal477735b45b070062c5df1d72c43d48f5)): ?>
<?php $attributes = $__attributesOriginal477735b45b070062c5df1d72c43d48f5; ?>
<?php unset($__attributesOriginal477735b45b070062c5df1d72c43d48f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal477735b45b070062c5df1d72c43d48f5)): ?>
<?php $component = $__componentOriginal477735b45b070062c5df1d72c43d48f5; ?>
<?php unset($__componentOriginal477735b45b070062c5df1d72c43d48f5); ?>
<?php endif; ?>
                </div>

                <div class="text-xl font-bold dark:text-white">
                    <?php echo e(trans('sales::app.dashboard.title')); ?>

                </div>
            </div>

            <div class="flex items-center gap-x-2.5">
                <!-- Filter Controls -->
                <v-sales-dashboard-filters></v-sales-dashboard-filters>
            </div>
        </div>

        <?php echo view_render_event('sales.dashboard.index.header.after'); ?>


        <!-- Sales Dashboard -->
        <v-sales-dashboard>
            <div class="flex flex-col gap-4">
                <!-- Shimmer for Overview Cards -->
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <?php for($i = 0; $i < 4; $i++): ?>
                        <?php if (isset($component)) { $__componentOriginal749a64c217134518f8b1b509819c7e9f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal749a64c217134518f8b1b509819c7e9f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.dashboard.index.over-all','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.dashboard.index.over-all'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal749a64c217134518f8b1b509819c7e9f)): ?>
<?php $attributes = $__attributesOriginal749a64c217134518f8b1b509819c7e9f; ?>
<?php unset($__attributesOriginal749a64c217134518f8b1b509819c7e9f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal749a64c217134518f8b1b509819c7e9f)): ?>
<?php $component = $__componentOriginal749a64c217134518f8b1b509819c7e9f; ?>
<?php unset($__componentOriginal749a64c217134518f8b1b509819c7e9f); ?>
<?php endif; ?>
                    <?php endfor; ?>
                </div>

                <!-- Shimmer for Charts -->
                <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                    <?php if (isset($component)) { $__componentOriginal9c2401f601e6d79e253560e6742ec3e4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9c2401f601e6d79e253560e6742ec3e4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.dashboard.index.revenue','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.dashboard.index.revenue'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9c2401f601e6d79e253560e6742ec3e4)): ?>
<?php $attributes = $__attributesOriginal9c2401f601e6d79e253560e6742ec3e4; ?>
<?php unset($__attributesOriginal9c2401f601e6d79e253560e6742ec3e4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9c2401f601e6d79e253560e6742ec3e4)): ?>
<?php $component = $__componentOriginal9c2401f601e6d79e253560e6742ec3e4; ?>
<?php unset($__componentOriginal9c2401f601e6d79e253560e6742ec3e4); ?>
<?php endif; ?>
                    <?php if (isset($component)) { $__componentOriginal9c2401f601e6d79e253560e6742ec3e4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9c2401f601e6d79e253560e6742ec3e4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.dashboard.index.revenue','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.dashboard.index.revenue'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9c2401f601e6d79e253560e6742ec3e4)): ?>
<?php $attributes = $__attributesOriginal9c2401f601e6d79e253560e6742ec3e4; ?>
<?php unset($__attributesOriginal9c2401f601e6d79e253560e6742ec3e4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9c2401f601e6d79e253560e6742ec3e4)): ?>
<?php $component = $__componentOriginal9c2401f601e6d79e253560e6742ec3e4; ?>
<?php unset($__componentOriginal9c2401f601e6d79e253560e6742ec3e4); ?>
<?php endif; ?>
                </div>

                <!-- Shimmer for Quick Actions -->
                <?php if (isset($component)) { $__componentOriginal9c2401f601e6d79e253560e6742ec3e4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9c2401f601e6d79e253560e6742ec3e4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.shimmer.dashboard.index.revenue','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::shimmer.dashboard.index.revenue'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9c2401f601e6d79e253560e6742ec3e4)): ?>
<?php $attributes = $__attributesOriginal9c2401f601e6d79e253560e6742ec3e4; ?>
<?php unset($__attributesOriginal9c2401f601e6d79e253560e6742ec3e4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9c2401f601e6d79e253560e6742ec3e4)): ?>
<?php $component = $__componentOriginal9c2401f601e6d79e253560e6742ec3e4; ?>
<?php unset($__componentOriginal9c2401f601e6d79e253560e6742ec3e4); ?>
<?php endif; ?>
            </div>
        </v-sales-dashboard>

        <?php echo view_render_event('sales.dashboard.index.content.after'); ?>

    </div>
<?php $__env->stopSection(); ?>

<?php if (! $__env->hasRenderedOnce('09fb6046-adad-4fd5-b340-11b08c645107')): $__env->markAsRenderedOnce('09fb6046-adad-4fd5-b340-11b08c645107');
$__env->startPush('scripts'); ?>
    <script
        type="text/x-template"
        id="v-sales-dashboard-filters-template"
    >
        <div class="flex items-center gap-2">
            <!-- Period Filter -->
            <select
                v-model="filters.period"
                @change="applyFilters"
                class="rounded border border-gray-300 px-3 py-1 text-sm dark:border-gray-600 dark:bg-gray-800"
            >
                <option value="daily"><?php echo e(trans('admin::app.layouts.daily')); ?></option>
                <option value="weekly"><?php echo e(trans('admin::app.layouts.weekly')); ?></option>
                <option value="monthly"><?php echo e(trans('admin::app.layouts.monthly')); ?></option>
                <option value="quarterly"><?php echo e(trans('admin::app.layouts.quarterly')); ?></option>
            </select>

            <!-- Date Range -->
            <input
                type="date"
                v-model="filters.date_from"
                @change="applyFilters"
                class="rounded border border-gray-300 px-3 py-1 text-sm dark:border-gray-600 dark:bg-gray-800"
            />
            <span class="text-gray-500">to</span>
            <input
                type="date"
                v-model="filters.date_to"
                @change="applyFilters"
                class="rounded border border-gray-300 px-3 py-1 text-sm dark:border-gray-600 dark:bg-gray-800"
            />
        </div>
    </script>

    <script
        type="text/x-template"
        id="v-sales-dashboard-template"
    >
        <div v-if="isLoading">
            <slot></slot>
        </div>

        <div v-else class="flex flex-col gap-4">
            <!-- Overview Cards -->
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                <!-- Total Targets -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo e(trans('sales::app.dashboard.total-targets')); ?></p>
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ overview.targets.total_targets }}</p>
                            <p class="text-xs text-gray-500">
                                {{ overview.targets.active_targets }} active
                            </p>
                        </div>
                        <div class="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
                            <span class="icon-target text-xl text-blue-600 dark:text-blue-400"></span>
                        </div>
                    </div>
                </div>

                <!-- Achievement Rate -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo e(trans('sales::app.dashboard.performance-overview')); ?></p>
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ overview.targets.overall_achievement }}%</p>
                            <p class="text-xs text-gray-500">
                                {{ overview.targets.achieved_targets }} achieved
                            </p>
                        </div>
                        <div class="rounded-full bg-green-100 p-3 dark:bg-green-900">
                            <span class="icon-leads text-xl text-green-600 dark:text-green-400"></span>
                        </div>
                    </div>
                </div>

                <!-- Total Target Amount -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo e(trans('admin::app.layouts.total-target')); ?></p>
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ formatCurrency(overview.targets.total_target_amount) }}</p>
                            <p class="text-xs text-gray-500">
                                Target amount
                            </p>
                        </div>
                        <div class="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900">
                            <span class="icon-quote text-xl text-yellow-600 dark:text-yellow-400"></span>
                        </div>
                    </div>
                </div>

                <!-- Total Achieved -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo e(trans('admin::app.layouts.total-achieved')); ?></p>
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ formatCurrency(overview.targets.total_achieved_amount) }}</p>
                            <p class="text-xs text-gray-500">
                                Achieved amount
                            </p>
                        </div>
                        <div class="rounded-full bg-purple-100 p-3 dark:bg-purple-900">
                            <span class="icon-activity text-xl text-purple-600 dark:text-purple-400"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                <!-- Targets Over Time -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="mb-4 flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
                            <?php echo e(trans('admin::app.layouts.targets-over-time')); ?>

                        </h3>
                    </div>

                    <?php if (isset($component)) { $__componentOriginalb8c5a56cc7464ac234c10f1e17210837 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb8c5a56cc7464ac234c10f1e17210837 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.charts.line','data' => [':labels' => 'targetsOverTimeLabels',':datasets' => 'targetsOverTimeDatasets','aspectRatio' => 2]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::charts.line'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([':labels' => 'targetsOverTimeLabels',':datasets' => 'targetsOverTimeDatasets','aspect-ratio' => 2]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb8c5a56cc7464ac234c10f1e17210837)): ?>
<?php $attributes = $__attributesOriginalb8c5a56cc7464ac234c10f1e17210837; ?>
<?php unset($__attributesOriginalb8c5a56cc7464ac234c10f1e17210837); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb8c5a56cc7464ac234c10f1e17210837)): ?>
<?php $component = $__componentOriginalb8c5a56cc7464ac234c10f1e17210837; ?>
<?php unset($__componentOriginalb8c5a56cc7464ac234c10f1e17210837); ?>
<?php endif; ?>
                </div>

                <!-- Performance Distribution -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <div class="mb-4 flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
                            <?php echo e(trans('admin::app.layouts.performance-distribution')); ?>

                        </h3>
                    </div>

                    <?php if (isset($component)) { $__componentOriginal3ab4661c9a7b9c9428d04459c8f925c5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3ab4661c9a7b9c9428d04459c8f925c5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.charts.doughnut','data' => [':labels' => 'performanceDistributionLabels',':datasets' => 'performanceDistributionDatasets']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin::charts.doughnut'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([':labels' => 'performanceDistributionLabels',':datasets' => 'performanceDistributionDatasets']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3ab4661c9a7b9c9428d04459c8f925c5)): ?>
<?php $attributes = $__attributesOriginal3ab4661c9a7b9c9428d04459c8f925c5; ?>
<?php unset($__attributesOriginal3ab4661c9a7b9c9428d04459c8f925c5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3ab4661c9a7b9c9428d04459c8f925c5)): ?>
<?php $component = $__componentOriginal3ab4661c9a7b9c9428d04459c8f925c5; ?>
<?php unset($__componentOriginal3ab4661c9a7b9c9428d04459c8f925c5); ?>
<?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions & Recent Activity -->
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                <!-- Quick Actions -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <h3 class="mb-4 text-lg font-semibold text-gray-800 dark:text-white">
                        <?php echo e(trans('admin::app.layouts.quick-actions')); ?>

                    </h3>

                    <div class="grid grid-cols-2 gap-3">
                        <a
                            href="<?php echo e(route('admin.sales.targets.create')); ?>"
                            class="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
                        >
                            <div class="rounded-full bg-blue-100 p-2 dark:bg-blue-900">
                                <span class="icon-target text-blue-600 dark:text-blue-400"></span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-800 dark:text-white">Create Target</p>
                                <p class="text-xs text-gray-500">Set new sales target</p>
                            </div>
                        </a>

                        <a
                            href="<?php echo e(route('admin.sales.performance.index')); ?>"
                            class="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
                        >
                            <div class="rounded-full bg-green-100 p-2 dark:bg-green-900">
                                <span class="icon-leads text-green-600 dark:text-green-400"></span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-800 dark:text-white">View Performance</p>
                                <p class="text-xs text-gray-500">Check team performance</p>
                            </div>
                        </a>

                        <a
                            href="<?php echo e(route('admin.sales.reports.create')); ?>"
                            class="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
                        >
                            <div class="rounded-full bg-yellow-100 p-2 dark:bg-yellow-900">
                                <span class="icon-quote text-yellow-600 dark:text-yellow-400"></span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-800 dark:text-white">Generate Report</p>
                                <p class="text-xs text-gray-500">Create sales report</p>
                            </div>
                        </a>

                        <a
                            href="<?php echo e(route('admin.sales.performance.leaderboard')); ?>"
                            class="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800"
                        >
                            <div class="rounded-full bg-purple-100 p-2 dark:bg-purple-900">
                                <span class="icon-activity text-purple-600 dark:text-purple-400"></span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-800 dark:text-white">Leaderboard</p>
                                <p class="text-xs text-gray-500">View top performers</p>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Recent Targets -->
                <div class="box-shadow rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-800 dark:bg-gray-900">
                    <h3 class="mb-4 text-lg font-semibold text-gray-800 dark:text-white">
                        <?php echo e(trans('admin::app.layouts.recent-targets')); ?>

                    </h3>

                    <div class="space-y-3">
                        <div
                            v-for="target in recentTargets"
                            :key="target.id"
                            class="flex items-center justify-between rounded-lg border border-gray-100 p-3 dark:border-gray-700"
                        >
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-800 dark:text-white">
                                    {{ target.name }}
                                </p>
                                <p class="text-xs text-gray-500">
                                    {{ target.assignee_name }} • {{ target.period_type }}
                                </p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-800 dark:text-white">
                                    {{ target.progress_percentage }}%
                                </p>
                                <div class="mt-1 h-1 w-16 rounded-full bg-gray-200 dark:bg-gray-700">
                                    <div
                                        class="h-1 rounded-full bg-blue-600"
                                        :style="{ width: Math.min(100, target.progress_percentage) + '%' }"
                                    ></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-sales-dashboard-filters', {
            template: '#v-sales-dashboard-filters-template',

            data() {
                return {
                    filters: {
                        period: 'monthly',
                        date_from: this.getDefaultDateFrom(),
                        date_to: this.getDefaultDateTo(),
                    },
                };
            },

            methods: {
                applyFilters() {
                    this.$emitter.emit('sales-dashboard-filter-updated', this.filters);
                },

                getDefaultDateFrom() {
                    const date = new Date();
                    date.setMonth(date.getMonth() - 6);
                    return date.toISOString().split('T')[0];
                },

                getDefaultDateTo() {
                    return new Date().toISOString().split('T')[0];
                },
            },

            mounted() {
                this.applyFilters();
            },
        });

        app.component('v-sales-dashboard', {
            template: '#v-sales-dashboard-template',

            data() {
                return {
                    isLoading: true,
                    overview: {
                        targets: {},
                        performance: {},
                    },
                    targetsOverTime: [],
                    performanceDistribution: [],
                    recentTargets: [],
                };
            },

            computed: {
                targetsOverTimeLabels() {
                    return this.targetsOverTime.map(item => item.period);
                },

                targetsOverTimeDatasets() {
                    return [{
                        label: 'Targets Created',
                        data: this.targetsOverTime.map(item => item.count),
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                    }];
                },

                performanceDistributionLabels() {
                    return ['100%+', '75-99%', '50-74%', '<50%'];
                },

                performanceDistributionDatasets() {
                    return [{
                        data: [25, 35, 25, 15], // Sample data
                        backgroundColor: ['#10B981', '#3B82F6', '#F59E0B', '#EF4444'],
                    }];
                },
            },

            mounted() {
                this.loadData({});

                this.$emitter.on('sales-dashboard-filter-updated', this.loadData);
            },

            methods: {
                loadData(filters) {
                    this.isLoading = true;

                    this.$axios.get("<?php echo e(route('admin.sales.dashboard.stats')); ?>", {
                        params: { type: 'overview', ...filters }
                    }).then(response => {
                        this.overview = response.data;
                        this.isLoading = false;
                    });

                    // Load recent targets
                    this.$axios.get("<?php echo e(route('admin.sales.targets.index')); ?>", {
                        params: { per_page: 5, sort: 'created_at', order: 'desc' }
                    }).then(response => {
                        this.recentTargets = response.data.data;
                    });
                },

                formatCurrency(amount) {
                    return new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD'
                    }).format(amount);
                },
            },
        });
    </script>
<?php $__env->stopPush(); endif; ?>

<?php echo $__env->make('sales::layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\CRM\laravel-crm2\laravel-crm\packages\Webkul\Sales\src/resources/views/dashboard/index.blade.php ENDPATH**/ ?>