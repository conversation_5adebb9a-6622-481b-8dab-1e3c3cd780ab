<?php return array (
  'app' => 
  array (
    'name' => 'Krayin CRM',
    'env' => 'local',
    'debug' => true,
    'url' => 'http://localhost',
    'admin_path' => 'admin',
    'asset_url' => NULL,
    'timezone' => 'Asia/Kolkata',
    'locale' => 'en',
    'available_locales' => 
    array (
      'ar' => 'Arabic',
      'en' => 'English',
      'es' => 'Español',
      'fa' => 'Persian',
      'pt_BR' => 'Portuguese',
      'tr' => 'Türkçe',
      'vi' => 'Vietnamese',
    ),
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'currency' => 'USD',
    'key' => 'base64:jPdaMuDCy/BCPEpQYvifV2ZxgKwm2YLYDK27RfjAWFU=',
    'cipher' => 'AES-256-CBC',
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Cookie\\CookieServiceProvider',
      6 => 'Illuminate\\Database\\DatabaseServiceProvider',
      7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      10 => 'Illuminate\\Hashing\\HashServiceProvider',
      11 => 'Illuminate\\Mail\\MailServiceProvider',
      12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      14 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      15 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      16 => 'Illuminate\\Queue\\QueueServiceProvider',
      17 => 'Illuminate\\Redis\\RedisServiceProvider',
      18 => 'Illuminate\\Session\\SessionServiceProvider',
      19 => 'Illuminate\\Translation\\TranslationServiceProvider',
      20 => 'Illuminate\\Validation\\ValidationServiceProvider',
      21 => 'Illuminate\\View\\ViewServiceProvider',
      22 => 'Barryvdh\\DomPDF\\ServiceProvider',
      23 => 'Konekt\\Concord\\ConcordServiceProvider',
      24 => 'Prettus\\Repository\\Providers\\RepositoryServiceProvider',
      25 => 'App\\Providers\\AppServiceProvider',
      26 => 'App\\Providers\\AuthServiceProvider',
      27 => 'App\\Providers\\EventServiceProvider',
      28 => 'App\\Providers\\RouteServiceProvider',
      29 => 'Webkul\\Activity\\Providers\\ActivityServiceProvider',
      30 => 'Webkul\\Admin\\Providers\\AdminServiceProvider',
      31 => 'Webkul\\Attribute\\Providers\\AttributeServiceProvider',
      32 => 'Webkul\\Automation\\Providers\\WorkflowServiceProvider',
      33 => 'Webkul\\Contact\\Providers\\ContactServiceProvider',
      34 => 'Webkul\\Core\\Providers\\CoreServiceProvider',
      35 => 'Webkul\\DataGrid\\Providers\\DataGridServiceProvider',
      36 => 'Webkul\\DataTransfer\\Providers\\DataTransferServiceProvider',
      37 => 'Webkul\\EmailTemplate\\Providers\\EmailTemplateServiceProvider',
      38 => 'Webkul\\Email\\Providers\\EmailServiceProvider',
      39 => 'Webkul\\Marketing\\Providers\\MarketingServiceProvider',
      40 => 'Webkul\\Installer\\Providers\\InstallerServiceProvider',
      41 => 'Webkul\\Lead\\Providers\\LeadServiceProvider',
      42 => 'Webkul\\Product\\Providers\\ProductServiceProvider',
      43 => 'Webkul\\Quote\\Providers\\QuoteServiceProvider',
      44 => 'Webkul\\Tag\\Providers\\TagServiceProvider',
      45 => 'Webkul\\User\\Providers\\UserServiceProvider',
      46 => 'Webkul\\Warehouse\\Providers\\WarehouseServiceProvider',
      47 => 'Webkul\\WebForm\\Providers\\WebFormServiceProvider',
      48 => 'Webkul\\Sales\\Providers\\SalesServiceProvider',
      49 => 'Webkul\\Sales\\Providers\\SalesMenuServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Arr' => 'Illuminate\\Support\\Arr',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'Date' => 'Illuminate\\Support\\Facades\\Date',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Http' => 'Illuminate\\Support\\Facades\\Http',
      'Js' => 'Illuminate\\Support\\Js',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Number' => 'Illuminate\\Support\\Number',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Process' => 'Illuminate\\Support\\Facades\\Process',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'RateLimiter' => 'Illuminate\\Support\\Facades\\RateLimiter',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'Str' => 'Illuminate\\Support\\Str',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Vite' => 'Illuminate\\Support\\Facades\\Vite',
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'user',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'user' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'sanctum' => 
      array (
        'driver' => 'sanctum',
        'provider' => NULL,
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'Webkul\\User\\Models\\User',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'user_password_resets',
        'expire' => 60,
      ),
    ),
    'password_timeout' => 10800,
  ),
  'breadcrumbs' => 
  array (
    'view' => 'breadcrumbs::bootstrap5',
    'files' => 
    array (
      0 => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\routes/breadcrumbs.php',
      1 => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\packages/Webkul/Sales/src/Config/breadcrumbs.php',
    ),
    'unnamed-route-exception' => true,
    'missing-route-bound-breadcrumb-exception' => true,
    'invalid-named-breadcrumb-exception' => true,
    'manager-class' => 'Diglactic\\Breadcrumbs\\Manager',
    'generator-class' => 'Diglactic\\Breadcrumbs\\Generator',
    'sales.dashboard' => 
    array (
      'name' => 'sales::app.dashboard.title',
      'route' => 'admin.sales.dashboard.index',
    ),
    'sales.targets' => 
    array (
      'name' => 'sales::app.targets.title',
      'route' => 'admin.sales.targets.index',
    ),
    'sales.targets.create' => 
    array (
      'name' => 'sales::app.targets.create-title',
      'route' => 'admin.sales.targets.create',
    ),
    'sales.targets.edit' => 
    array (
      'name' => 'sales::app.targets.edit-title',
      'route' => 'admin.sales.targets.edit',
    ),
    'sales.performance' => 
    array (
      'name' => 'sales::app.performance.title',
      'route' => 'admin.sales.performance.index',
    ),
    'sales.reports' => 
    array (
      'name' => 'sales::app.reports.title',
      'route' => 'admin.sales.reports.index',
    ),
    'sales.reports.create' => 
    array (
      'name' => 'sales::app.reports.create-title',
      'route' => 'admin.sales.reports.create',
    ),
  ),
  'broadcasting' => 
  array (
    'default' => 'log',
    'connections' => 
    array (
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => '',
        'secret' => '',
        'app_id' => '',
        'options' => 
        array (
          'cluster' => 'mt1',
          'useTLS' => true,
        ),
      ),
      'ably' => 
      array (
        'driver' => 'ably',
        'key' => NULL,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
    ),
  ),
  'cache' => 
  array (
    'default' => 'file',
    'stores' => 
    array (
      'apc' => 
      array (
        'driver' => 'apc',
      ),
      'array' => 
      array (
        'driver' => 'array',
        'serialize' => false,
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'cache',
        'connection' => NULL,
        'lock_connection' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'table' => 'cache',
        'endpoint' => NULL,
      ),
    ),
    'prefix' => 'krayin_crm_cache',
  ),
  'concord' => 
  array (
    'modules' => 
    array (
      0 => 'Webkul\\Activity\\Providers\\ModuleServiceProvider',
      1 => 'Webkul\\Admin\\Providers\\ModuleServiceProvider',
      2 => 'Webkul\\Attribute\\Providers\\ModuleServiceProvider',
      3 => 'Webkul\\Automation\\Providers\\ModuleServiceProvider',
      4 => 'Webkul\\Contact\\Providers\\ModuleServiceProvider',
      5 => 'Webkul\\Core\\Providers\\ModuleServiceProvider',
      6 => 'Webkul\\DataGrid\\Providers\\ModuleServiceProvider',
      7 => 'Webkul\\EmailTemplate\\Providers\\ModuleServiceProvider',
      8 => 'Webkul\\Email\\Providers\\ModuleServiceProvider',
      9 => 'Webkul\\Lead\\Providers\\ModuleServiceProvider',
      10 => 'Webkul\\Product\\Providers\\ModuleServiceProvider',
      11 => 'Webkul\\Quote\\Providers\\ModuleServiceProvider',
      12 => 'Webkul\\Tag\\Providers\\ModuleServiceProvider',
      13 => 'Webkul\\User\\Providers\\ModuleServiceProvider',
      14 => 'Webkul\\Warehouse\\Providers\\ModuleServiceProvider',
      15 => 'Webkul\\WebForm\\Providers\\ModuleServiceProvider',
      16 => 'Webkul\\DataTransfer\\Providers\\ModuleServiceProvider',
      17 => 'Webkul\\Sales\\Providers\\ModuleServiceProvider',
    ),
    'register_route_models' => true,
  ),
  'cors' => 
  array (
    'paths' => 
    array (
      0 => 'admin/web-forms/forms/*',
    ),
    'allowed_methods' => 
    array (
      0 => '*',
    ),
    'allowed_origins' => 
    array (
      0 => '*',
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => '*',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 0,
    'supports_credentials' => false,
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'url' => NULL,
        'database' => 'laravel-crm',
        'prefix' => '',
        'foreign_key_constraints' => true,
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'laravel-crm',
        'username' => 'root',
        'password' => 'root@123',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => false,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'laravel-crm',
        'username' => 'root',
        'password' => 'root@123',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
        'schema' => 'public',
        'sslmode' => 'prefer',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'laravel-crm',
        'username' => 'root',
        'password' => 'root@123',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
      ),
    ),
    'migrations' => 'migrations',
    'redis' => 
    array (
      'client' => 'phpredis',
      'options' => 
      array (
        'cluster' => 'redis',
        'prefix' => 'krayin_crm_database_',
      ),
      'default' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'password' => NULL,
        'port' => '6379',
        'database' => '0',
      ),
      'cache' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'password' => NULL,
        'port' => '6379',
        'database' => '1',
      ),
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'public',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\app',
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\app/public',
        'url' => 'http://localhost/storage',
        'visibility' => 'public',
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'bucket' => '',
        'url' => NULL,
        'endpoint' => NULL,
      ),
    ),
    'links' => 
    array (
      'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\public\\storage' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\app/public',
    ),
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => 10,
    ),
    'argon' => 
    array (
      'memory' => 1024,
      'threads' => 2,
      'time' => 2,
    ),
  ),
  'imap' => 
  array (
    'default' => 'default',
    'date_format' => 'd-M-Y',
    'accounts' => 
    array (
      'default' => 
      array (
        'host' => 'localhost',
        'port' => 993,
        'protocol' => 'imap',
        'encryption' => 'ssl',
        'validate_cert' => true,
        'username' => '<EMAIL>',
        'password' => '',
        'authentication' => NULL,
        'proxy' => 
        array (
          'socket' => NULL,
          'request_fulluri' => false,
          'username' => NULL,
          'password' => NULL,
        ),
        'timeout' => 30,
        'extensions' => 
        array (
        ),
      ),
    ),
    'options' => 
    array (
      'delimiter' => '/',
      'fetch' => 2,
      'sequence' => 1,
      'fetch_body' => true,
      'fetch_flags' => true,
      'soft_fail' => false,
      'rfc822' => true,
      'debug' => false,
      'uid_cache' => true,
      'boundary' => '/boundary=(.*?(?=;)|(.*))/i',
      'message_key' => 'list',
      'fetch_order' => 'asc',
      'dispositions' => 
      array (
        0 => 'attachment',
        1 => 'inline',
      ),
      'common_folders' => 
      array (
        'root' => 'INBOX',
        'junk' => 'INBOX/Junk',
        'draft' => 'INBOX/Drafts',
        'sent' => 'INBOX/Sent',
        'trash' => 'INBOX/Trash',
      ),
      'decoder' => 
      array (
        'message' => 'utf-8',
        'attachment' => 'utf-8',
      ),
      'open' => 
      array (
      ),
    ),
    'flags' => 
    array (
      0 => 'recent',
      1 => 'flagged',
      2 => 'answered',
      3 => 'deleted',
      4 => 'seen',
      5 => 'draft',
    ),
    'events' => 
    array (
      'message' => 
      array (
        'new' => 'Webklex\\IMAP\\Events\\MessageNewEvent',
        'moved' => 'Webklex\\IMAP\\Events\\MessageMovedEvent',
        'copied' => 'Webklex\\IMAP\\Events\\MessageCopiedEvent',
        'deleted' => 'Webklex\\IMAP\\Events\\MessageDeletedEvent',
        'restored' => 'Webklex\\IMAP\\Events\\MessageRestoredEvent',
      ),
      'folder' => 
      array (
        'new' => 'Webklex\\IMAP\\Events\\FolderNewEvent',
        'moved' => 'Webklex\\IMAP\\Events\\FolderMovedEvent',
        'deleted' => 'Webklex\\IMAP\\Events\\FolderDeletedEvent',
      ),
      'flag' => 
      array (
        'new' => 'Webklex\\IMAP\\Events\\FlagNewEvent',
        'deleted' => 'Webklex\\IMAP\\Events\\FlagDeletedEvent',
      ),
    ),
    'masks' => 
    array (
      'message' => 'Webklex\\PHPIMAP\\Support\\Masks\\MessageMask',
      'attachment' => 'Webklex\\PHPIMAP\\Support\\Masks\\AttachmentMask',
    ),
  ),
  'krayin-vite' => 
  array (
    'viters' => 
    array (
      'admin' => 
      array (
        'hot_file' => 'admin-vite.hot',
        'build_directory' => 'admin/build',
        'package_assets_directory' => 'src/Resources/assets',
      ),
      'installer' => 
      array (
        'hot_file' => 'installer-vite.hot',
        'build_directory' => 'installer/build',
        'package_assets_directory' => 'src/Resources/assets',
      ),
      'webform' => 
      array (
        'hot_file' => 'webform-vite.hot',
        'build_directory' => 'webform/build',
        'package_assets_directory' => 'src/Resources/assets',
      ),
    ),
  ),
  'logging' => 
  array (
    'default' => 'stack',
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'single',
        ),
        'ignore_exceptions' => false,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\logs/laravel.log',
        'level' => 'debug',
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => 14,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'debug',
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'formatter' => NULL,
        'with' => 
        array (
          'stream' => 'php://stderr',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
      ),
      'null' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
      ),
      'emergency' => 
      array (
        'path' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\logs/laravel.log',
      ),
    ),
  ),
  'mail' => 
  array (
    'default' => 'smtp',
    'mailers' => 
    array (
      'smtp' => 
      array (
        'transport' => 'smtp',
        'host' => 'mailhog',
        'port' => '1025',
        'encryption' => NULL,
        'username' => NULL,
        'password' => NULL,
        'timeout' => NULL,
        'verify_peer' => false,
      ),
      'ses' => 
      array (
        'transport' => 'ses',
      ),
      'mailgun' => 
      array (
        'transport' => 'mailgun',
      ),
      'postmark' => 
      array (
        'transport' => 'postmark',
      ),
      'sendmail' => 
      array (
        'transport' => 'sendmail',
        'path' => '/usr/sbin/sendmail -bs -i',
      ),
      'log' => 
      array (
        'transport' => 'log',
        'channel' => NULL,
      ),
      'array' => 
      array (
        'transport' => 'array',
      ),
      'failover' => 
      array (
        'transport' => 'failover',
        'mailers' => 
        array (
          0 => 'smtp',
          1 => 'log',
        ),
      ),
    ),
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => '${APP_NAME}',
    ),
    'domain' => 'webkul.com',
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\resources\\views/vendor/mail',
      ),
    ),
  ),
  'mail-receiver' => 
  array (
    'default' => 'sendgrid',
  ),
  'queue' => 
  array (
    'default' => 'sync',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => '',
        'secret' => '',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'your-queue-name',
        'suffix' => NULL,
        'region' => 'us-east-1',
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
      ),
    ),
    'failed' => 
    array (
      'driver' => 'database-uuids',
      'database' => 'mysql',
      'table' => 'failed_jobs',
    ),
  ),
  'repository' => 
  array (
    'pagination' => 
    array (
      'limit' => 15,
    ),
    'fractal' => 
    array (
      'params' => 
      array (
        'include' => 'include',
      ),
      'serializer' => 'League\\Fractal\\Serializer\\DataArraySerializer',
    ),
    'cache' => 
    array (
      'enabled' => false,
      'minutes' => 30,
      'repository' => 'cache',
      'clean' => 
      array (
        'enabled' => true,
        'on' => 
        array (
          'create' => true,
          'update' => true,
          'delete' => true,
        ),
      ),
      'params' => 
      array (
        'skipCache' => 'skipCache',
      ),
      'allowed' => 
      array (
        'only' => NULL,
        'except' => NULL,
      ),
    ),
    'criteria' => 
    array (
      'acceptedConditions' => 
      array (
        0 => '=',
        1 => 'like',
        2 => 'in',
      ),
      'params' => 
      array (
        'search' => 'search',
        'searchFields' => 'searchFields',
        'filter' => 'filter',
        'orderBy' => 'orderBy',
        'sortedBy' => 'sortedBy',
        'with' => 'with',
        'searchJoin' => 'searchJoin',
        'withCount' => 'withCount',
      ),
    ),
    'generator' => 
    array (
      'basePath' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\app',
      'rootNamespace' => 'App\\',
      'stubsOverridePath' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\app',
      'paths' => 
      array (
        'models' => 'Entities',
        'repositories' => 'Repositories',
        'interfaces' => 'Repositories',
        'transformers' => 'Transformers',
        'presenters' => 'Presenters',
        'validators' => 'Validators',
        'controllers' => 'Http/Controllers',
        'provider' => 'RepositoryServiceProvider',
        'criteria' => 'Criteria',
      ),
    ),
  ),
  'sanctum' => 
  array (
    'stateful' => 
    array (
      0 => 'localhost',
      1 => 'localhost:3000',
      2 => '127.0.0.1',
      3 => '127.0.0.1:8000',
      4 => '::1',
      5 => 'localhost',
    ),
    'guard' => 
    array (
      0 => 'user',
    ),
    'expiration' => NULL,
    'token_prefix' => '',
    'middleware' => 
    array (
      'verify_csrf_token' => 'App\\Http\\Middleware\\VerifyCsrfToken',
      'encrypt_cookies' => 'App\\Http\\Middleware\\EncryptCookies',
    ),
  ),
  'services' => 
  array (
    'mailgun' => 
    array (
      'domain' => NULL,
      'secret' => NULL,
      'endpoint' => 'api.mailgun.net',
    ),
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'ses' => 
    array (
      'key' => '',
      'secret' => '',
      'region' => 'us-east-1',
    ),
  ),
  'session' => 
  array (
    'driver' => 'file',
    'lifetime' => '120',
    'expire_on_close' => false,
    'encrypt' => false,
    'files' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'krayin_crm_session',
    'path' => '/',
    'domain' => NULL,
    'secure' => NULL,
    'http_only' => true,
    'same_site' => 'lax',
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\resources\\views',
    ),
    'compiled' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\framework\\views',
  ),
  'debugbar' => 
  array (
    'enabled' => NULL,
    'hide_empty_tabs' => true,
    'except' => 
    array (
      0 => 'telescope*',
      1 => 'horizon*',
    ),
    'storage' => 
    array (
      'enabled' => true,
      'open' => NULL,
      'driver' => 'file',
      'path' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\debugbar',
      'connection' => NULL,
      'provider' => '',
      'hostname' => '127.0.0.1',
      'port' => 2304,
    ),
    'editor' => 'phpstorm',
    'remote_sites_path' => NULL,
    'local_sites_path' => NULL,
    'include_vendors' => true,
    'capture_ajax' => true,
    'add_ajax_timing' => false,
    'ajax_handler_auto_show' => true,
    'ajax_handler_enable_tab' => true,
    'defer_datasets' => false,
    'error_handler' => false,
    'clockwork' => false,
    'collectors' => 
    array (
      'phpinfo' => false,
      'messages' => true,
      'time' => true,
      'memory' => true,
      'exceptions' => true,
      'log' => true,
      'db' => true,
      'views' => true,
      'route' => false,
      'auth' => false,
      'gate' => true,
      'session' => false,
      'symfony_request' => true,
      'mail' => true,
      'laravel' => true,
      'events' => false,
      'default_request' => false,
      'logs' => false,
      'files' => false,
      'config' => false,
      'cache' => false,
      'models' => true,
      'livewire' => true,
      'jobs' => false,
      'pennant' => false,
    ),
    'options' => 
    array (
      'time' => 
      array (
        'memory_usage' => false,
      ),
      'messages' => 
      array (
        'trace' => true,
      ),
      'memory' => 
      array (
        'reset_peak' => false,
        'with_baseline' => false,
        'precision' => 0,
      ),
      'auth' => 
      array (
        'show_name' => true,
        'show_guards' => true,
      ),
      'db' => 
      array (
        'with_params' => true,
        'exclude_paths' => 
        array (
        ),
        'backtrace' => true,
        'backtrace_exclude_paths' => 
        array (
        ),
        'timeline' => false,
        'duration_background' => true,
        'explain' => 
        array (
          'enabled' => false,
        ),
        'hints' => false,
        'show_copy' => true,
        'slow_threshold' => false,
        'memory_usage' => false,
        'soft_limit' => 100,
        'hard_limit' => 500,
      ),
      'mail' => 
      array (
        'timeline' => true,
        'show_body' => true,
      ),
      'views' => 
      array (
        'timeline' => true,
        'data' => false,
        'group' => 50,
        'exclude_paths' => 
        array (
          0 => 'vendor/filament',
        ),
      ),
      'route' => 
      array (
        'label' => true,
      ),
      'session' => 
      array (
        'hiddens' => 
        array (
        ),
      ),
      'symfony_request' => 
      array (
        'label' => true,
        'hiddens' => 
        array (
        ),
      ),
      'events' => 
      array (
        'data' => false,
      ),
      'logs' => 
      array (
        'file' => NULL,
      ),
      'cache' => 
      array (
        'values' => true,
      ),
    ),
    'inject' => true,
    'route_prefix' => '_debugbar',
    'route_middleware' => 
    array (
    ),
    'route_domain' => NULL,
    'theme' => 'auto',
    'debug_backtrace_limit' => 50,
  ),
  'dompdf' => 
  array (
    'show_warnings' => false,
    'public_path' => NULL,
    'convert_entities' => true,
    'options' => 
    array (
      'font_dir' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\fonts',
      'font_cache' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\fonts',
      'temp_dir' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp',
      'chroot' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm',
      'allowed_protocols' => 
      array (
        'file://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'http://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'https://' => 
        array (
          'rules' => 
          array (
          ),
        ),
      ),
      'log_output_file' => NULL,
      'enable_font_subsetting' => false,
      'pdf_backend' => 'CPDF',
      'default_media_type' => 'screen',
      'default_paper_size' => 'a4',
      'default_paper_orientation' => 'portrait',
      'default_font' => 'serif',
      'dpi' => 96,
      'enable_php' => false,
      'enable_javascript' => true,
      'enable_remote' => true,
      'font_height_ratio' => 1.1,
      'enable_html5_parser' => true,
    ),
  ),
  'excel' => 
  array (
    'exports' => 
    array (
      'chunk_size' => 1000,
      'pre_calculate_formulas' => false,
      'strict_null_comparison' => false,
      'csv' => 
      array (
        'delimiter' => ',',
        'enclosure' => '"',
        'line_ending' => '
',
        'use_bom' => false,
        'include_separator_line' => false,
        'excel_compatibility' => false,
        'output_encoding' => '',
        'test_auto_detect' => true,
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
    ),
    'imports' => 
    array (
      'read_only' => true,
      'ignore_empty' => false,
      'heading_row' => 
      array (
        'formatter' => 'slug',
      ),
      'csv' => 
      array (
        'delimiter' => NULL,
        'enclosure' => '"',
        'escape_character' => '\\',
        'contiguous' => false,
        'input_encoding' => 'guess',
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
      'cells' => 
      array (
        'middleware' => 
        array (
        ),
      ),
    ),
    'extension_detector' => 
    array (
      'xlsx' => 'Xlsx',
      'xlsm' => 'Xlsx',
      'xltx' => 'Xlsx',
      'xltm' => 'Xlsx',
      'xls' => 'Xls',
      'xlt' => 'Xls',
      'ods' => 'Ods',
      'ots' => 'Ods',
      'slk' => 'Slk',
      'xml' => 'Xml',
      'gnumeric' => 'Gnumeric',
      'htm' => 'Html',
      'html' => 'Html',
      'csv' => 'Csv',
      'tsv' => 'Csv',
      'pdf' => 'Dompdf',
    ),
    'value_binder' => 
    array (
      'default' => 'Maatwebsite\\Excel\\DefaultValueBinder',
    ),
    'cache' => 
    array (
      'driver' => 'memory',
      'batch' => 
      array (
        'memory_limit' => 60000,
      ),
      'illuminate' => 
      array (
        'store' => NULL,
      ),
      'default_ttl' => 10800,
    ),
    'transactions' => 
    array (
      'handler' => 'db',
      'db' => 
      array (
        'connection' => NULL,
      ),
    ),
    'temporary_files' => 
    array (
      'local_path' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm\\storage\\framework/cache/laravel-excel',
      'local_permissions' => 
      array (
      ),
      'remote_disk' => NULL,
      'remote_prefix' => NULL,
      'force_resync_remote' => NULL,
    ),
  ),
  'flare' => 
  array (
    'key' => NULL,
    'flare_middleware' => 
    array (
      0 => 'Spatie\\FlareClient\\FlareMiddleware\\RemoveRequestIp',
      1 => 'Spatie\\FlareClient\\FlareMiddleware\\AddGitInformation',
      2 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddNotifierName',
      3 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddEnvironmentInformation',
      4 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddExceptionInformation',
      5 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddDumps',
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddLogs' => 
      array (
        'maximum_number_of_collected_logs' => 200,
      ),
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddQueries' => 
      array (
        'maximum_number_of_collected_queries' => 200,
        'report_query_bindings' => true,
      ),
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddJobs' => 
      array (
        'max_chained_job_reporting_depth' => 5,
      ),
      6 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddContext',
      7 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddExceptionHandledStatus',
      'Spatie\\FlareClient\\FlareMiddleware\\CensorRequestBodyFields' => 
      array (
        'censor_fields' => 
        array (
          0 => 'password',
          1 => 'password_confirmation',
        ),
      ),
      'Spatie\\FlareClient\\FlareMiddleware\\CensorRequestHeaders' => 
      array (
        'headers' => 
        array (
          0 => 'API-KEY',
          1 => 'Authorization',
          2 => 'Cookie',
          3 => 'Set-Cookie',
          4 => 'X-CSRF-TOKEN',
          5 => 'X-XSRF-TOKEN',
        ),
      ),
    ),
    'send_logs_as_events' => true,
  ),
  'ignition' => 
  array (
    'editor' => 'phpstorm',
    'theme' => 'auto',
    'enable_share_button' => true,
    'register_commands' => false,
    'solution_providers' => 
    array (
      0 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\BadMethodCallSolutionProvider',
      1 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider',
      2 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\UndefinedPropertySolutionProvider',
      3 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\IncorrectValetDbCredentialsSolutionProvider',
      4 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingAppKeySolutionProvider',
      5 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\DefaultDbNameSolutionProvider',
      6 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\TableNotFoundSolutionProvider',
      7 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingImportSolutionProvider',
      8 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\InvalidRouteActionSolutionProvider',
      9 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\ViewNotFoundSolutionProvider',
      10 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\RunningLaravelDuskInProductionProvider',
      11 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingColumnSolutionProvider',
      12 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownValidationSolutionProvider',
      13 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingMixManifestSolutionProvider',
      14 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingViteManifestSolutionProvider',
      15 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingLivewireComponentSolutionProvider',
      16 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UndefinedViewVariableSolutionProvider',
      17 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\GenericLaravelExceptionSolutionProvider',
      18 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\OpenAiSolutionProvider',
      19 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SailNetworkSolutionProvider',
      20 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownMysql8CollationSolutionProvider',
      21 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownMariadbCollationSolutionProvider',
    ),
    'ignored_solution_providers' => 
    array (
    ),
    'enable_runnable_solutions' => NULL,
    'remote_sites_path' => 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CRM\\laravel-crm2\\laravel-crm',
    'local_sites_path' => '',
    'housekeeping_endpoint_prefix' => '_ignition',
    'settings_file_path' => '',
    'recorders' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\DumpRecorder',
      1 => 'Spatie\\LaravelIgnition\\Recorders\\JobRecorder\\JobRecorder',
      2 => 'Spatie\\LaravelIgnition\\Recorders\\LogRecorder\\LogRecorder',
      3 => 'Spatie\\LaravelIgnition\\Recorders\\QueryRecorder\\QueryRecorder',
    ),
    'open_ai_key' => NULL,
    'with_stack_frame_arguments' => true,
    'argument_reducers' => 
    array (
      0 => 'Spatie\\Backtrace\\Arguments\\Reducers\\BaseTypeArgumentReducer',
      1 => 'Spatie\\Backtrace\\Arguments\\Reducers\\ArrayArgumentReducer',
      2 => 'Spatie\\Backtrace\\Arguments\\Reducers\\StdClassArgumentReducer',
      3 => 'Spatie\\Backtrace\\Arguments\\Reducers\\EnumArgumentReducer',
      4 => 'Spatie\\Backtrace\\Arguments\\Reducers\\ClosureArgumentReducer',
      5 => 'Spatie\\Backtrace\\Arguments\\Reducers\\DateTimeArgumentReducer',
      6 => 'Spatie\\Backtrace\\Arguments\\Reducers\\DateTimeZoneArgumentReducer',
      7 => 'Spatie\\Backtrace\\Arguments\\Reducers\\SymphonyRequestArgumentReducer',
      8 => 'Spatie\\LaravelIgnition\\ArgumentReducers\\ModelArgumentReducer',
      9 => 'Spatie\\LaravelIgnition\\ArgumentReducers\\CollectionArgumentReducer',
      10 => 'Spatie\\Backtrace\\Arguments\\Reducers\\StringableArgumentReducer',
    ),
  ),
  'acl' => 
  array (
    0 => 
    array (
      'key' => 'sales',
      'name' => 'sales::app.acl.sales',
      'route' => 'admin.sales.dashboard.index',
      'sort' => 3,
    ),
    1 => 
    array (
      'key' => 'sales.targets',
      'name' => 'sales::app.acl.targets',
      'route' => 'admin.sales.targets.index',
      'sort' => 1,
    ),
    2 => 
    array (
      'key' => 'sales.targets.create',
      'name' => 'sales::app.acl.create',
      'route' => 
      array (
        0 => 'admin.sales.targets.create',
        1 => 'admin.sales.targets.store',
      ),
      'sort' => 1,
    ),
    3 => 
    array (
      'key' => 'sales.targets.edit',
      'name' => 'sales::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.sales.targets.edit',
        1 => 'admin.sales.targets.update',
      ),
      'sort' => 2,
    ),
    4 => 
    array (
      'key' => 'sales.targets.delete',
      'name' => 'sales::app.acl.delete',
      'route' => 'admin.sales.targets.delete',
      'sort' => 3,
    ),
    5 => 
    array (
      'key' => 'sales.performance',
      'name' => 'sales::app.acl.performance',
      'route' => 'admin.sales.performance.index',
      'sort' => 2,
    ),
    6 => 
    array (
      'key' => 'sales.performance.view',
      'name' => 'sales::app.acl.view',
      'route' => 'admin.sales.performance.view',
      'sort' => 1,
    ),
    7 => 
    array (
      'key' => 'sales.reports',
      'name' => 'sales::app.acl.reports',
      'route' => 'admin.sales.reports.index',
      'sort' => 3,
    ),
    8 => 
    array (
      'key' => 'sales.reports.create',
      'name' => 'sales::app.acl.create',
      'route' => 
      array (
        0 => 'admin.sales.reports.create',
        1 => 'admin.sales.reports.store',
      ),
      'sort' => 1,
    ),
    9 => 
    array (
      'key' => 'sales.reports.export',
      'name' => 'sales::app.acl.export',
      'route' => 'admin.sales.reports.export',
      'sort' => 2,
    ),
    10 => 
    array (
      'key' => 'settings.other_settings.web_forms',
      'name' => 'web_form::app.acl.title',
      'route' => 'admin.settings.web_forms.index',
      'sort' => 1,
    ),
    11 => 
    array (
      'key' => 'settings.other_settings.web_forms.view',
      'name' => 'web_form::app.acl.view',
      'route' => 'admin.settings.web_forms.view',
      'sort' => 1,
    ),
    12 => 
    array (
      'key' => 'settings.other_settings.web_forms.create',
      'name' => 'web_form::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.web_forms.create',
        1 => 'admin.settings.web_forms.store',
      ),
      'sort' => 2,
    ),
    13 => 
    array (
      'key' => 'settings.other_settings.web_forms.edit',
      'name' => 'web_form::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.web_forms.edit',
        1 => 'admin.settings.web_forms.update',
      ),
      'sort' => 3,
    ),
    14 => 
    array (
      'key' => 'settings.other_settings.web_forms.delete',
      'name' => 'web_form::app.acl.delete',
      'route' => 'admin.settings.web_forms.delete',
      'sort' => 4,
    ),
    15 => 
    array (
      'key' => 'dashboard',
      'name' => 'admin::app.layouts.dashboard',
      'route' => 'admin.dashboard.index',
      'sort' => 1,
    ),
    16 => 
    array (
      'key' => 'leads',
      'name' => 'admin::app.acl.leads',
      'route' => 'admin.leads.index',
      'sort' => 2,
    ),
    17 => 
    array (
      'key' => 'leads.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.leads.create',
        1 => 'admin.leads.store',
      ),
      'sort' => 1,
    ),
    18 => 
    array (
      'key' => 'leads.view',
      'name' => 'admin::app.acl.view',
      'route' => 'admin.leads.view',
      'sort' => 2,
    ),
    19 => 
    array (
      'key' => 'leads.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.leads.edit',
        1 => 'admin.leads.update',
        2 => 'admin.leads.mass_update',
      ),
      'sort' => 3,
    ),
    20 => 
    array (
      'key' => 'leads.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 
      array (
        0 => 'admin.leads.delete',
        1 => 'admin.leads.mass_delete',
      ),
      'sort' => 4,
    ),
    21 => 
    array (
      'key' => 'quotes',
      'name' => 'admin::app.acl.quotes',
      'route' => 'admin.quotes.index',
      'sort' => 3,
    ),
    22 => 
    array (
      'key' => 'quotes.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.quotes.create',
        1 => 'admin.quotes.store',
      ),
      'sort' => 1,
    ),
    23 => 
    array (
      'key' => 'quotes.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.quotes.edit',
        1 => 'admin.quotes.update',
      ),
      'sort' => 2,
    ),
    24 => 
    array (
      'key' => 'quotes.print',
      'name' => 'admin::app.acl.print',
      'route' => 'admin.quotes.print',
      'sort' => 3,
    ),
    25 => 
    array (
      'key' => 'quotes.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 
      array (
        0 => 'admin.quotes.delete',
        1 => 'admin.quotes.mass_delete',
      ),
      'sort' => 4,
    ),
    26 => 
    array (
      'key' => 'mail',
      'name' => 'admin::app.acl.mail',
      'route' => 'admin.mail.index',
      'sort' => 4,
    ),
    27 => 
    array (
      'key' => 'mail.inbox',
      'name' => 'admin::app.acl.inbox',
      'route' => 'admin.mail.index',
      'sort' => 1,
    ),
    28 => 
    array (
      'key' => 'mail.draft',
      'name' => 'admin::app.acl.draft',
      'route' => 'admin.mail.index',
      'sort' => 2,
    ),
    29 => 
    array (
      'key' => 'mail.outbox',
      'name' => 'admin::app.acl.outbox',
      'route' => 'admin.mail.index',
      'sort' => 3,
    ),
    30 => 
    array (
      'key' => 'mail.sent',
      'name' => 'admin::app.acl.sent',
      'route' => 'admin.mail.index',
      'sort' => 4,
    ),
    31 => 
    array (
      'key' => 'mail.trash',
      'name' => 'admin::app.acl.trash',
      'route' => 'admin.mail.index',
      'sort' => 5,
    ),
    32 => 
    array (
      'key' => 'mail.compose',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.mail.store',
      ),
      'sort' => 6,
    ),
    33 => 
    array (
      'key' => 'mail.view',
      'name' => 'admin::app.acl.view',
      'route' => 'admin.mail.view',
      'sort' => 7,
    ),
    34 => 
    array (
      'key' => 'mail.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 'admin.mail.update',
      'sort' => 8,
    ),
    35 => 
    array (
      'key' => 'mail.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 
      array (
        0 => 'admin.mail.delete',
        1 => 'admin.mail.mass_delete',
      ),
      'sort' => 9,
    ),
    36 => 
    array (
      'key' => 'activities',
      'name' => 'admin::app.acl.activities',
      'route' => 'admin.activities.index',
      'sort' => 5,
    ),
    37 => 
    array (
      'key' => 'activities.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.activities.create',
        1 => 'admin.activities.store',
      ),
      'sort' => 1,
    ),
    38 => 
    array (
      'key' => 'activities.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.activities.edit',
        1 => 'admin.activities.update',
        2 => 'admin.activities.mass_update',
      ),
      'sort' => 2,
    ),
    39 => 
    array (
      'key' => 'activities.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 
      array (
        0 => 'admin.activities.delete',
        1 => 'admin.activities.mass_delete',
      ),
      'sort' => 3,
    ),
    40 => 
    array (
      'key' => 'contacts',
      'name' => 'admin::app.acl.contacts',
      'route' => 'admin.contacts.users.index',
      'sort' => 6,
    ),
    41 => 
    array (
      'key' => 'contacts.persons',
      'name' => 'admin::app.acl.persons',
      'route' => 'admin.contacts.persons.index',
      'sort' => 1,
    ),
    42 => 
    array (
      'key' => 'contacts.persons.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.contacts.persons.create',
        1 => 'admin.contacts.persons.store',
      ),
      'sort' => 2,
    ),
    43 => 
    array (
      'key' => 'contacts.persons.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.contacts.persons.edit',
        1 => 'admin.contacts.persons.update',
      ),
      'sort' => 3,
    ),
    44 => 
    array (
      'key' => 'contacts.persons.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 
      array (
        0 => 'admin.contacts.persons.delete',
        1 => 'admin.contacts.persons.mass_delete',
      ),
      'sort' => 4,
    ),
    45 => 
    array (
      'key' => 'contacts.persons.view',
      'name' => 'admin::app.acl.view',
      'route' => 'admin.contacts.persons.view',
      'sort' => 5,
    ),
    46 => 
    array (
      'key' => 'contacts.organizations',
      'name' => 'admin::app.acl.organizations',
      'route' => 'admin.contacts.organizations.index',
      'sort' => 2,
    ),
    47 => 
    array (
      'key' => 'contacts.organizations.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.contacts.organizations.create',
        1 => 'admin.contacts.organizations.store',
      ),
      'sort' => 1,
    ),
    48 => 
    array (
      'key' => 'contacts.organizations.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.contacts.organizations.edit',
        1 => 'admin.contacts.organizations.update',
      ),
      'sort' => 2,
    ),
    49 => 
    array (
      'key' => 'contacts.organizations.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 
      array (
        0 => 'admin.contacts.organizations.delete',
        1 => 'admin.contacts.organizations.mass_delete',
      ),
      'sort' => 3,
    ),
    50 => 
    array (
      'key' => 'products',
      'name' => 'admin::app.acl.products',
      'route' => 'admin.products.index',
      'sort' => 7,
    ),
    51 => 
    array (
      'key' => 'products.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.products.create',
        1 => 'admin.products.store',
      ),
      'sort' => 1,
    ),
    52 => 
    array (
      'key' => 'products.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.products.edit',
        1 => 'admin.products.update',
      ),
      'sort' => 2,
    ),
    53 => 
    array (
      'key' => 'products.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 
      array (
        0 => 'admin.products.delete',
        1 => 'admin.products.mass_delete',
      ),
      'sort' => 3,
    ),
    54 => 
    array (
      'key' => 'products.view',
      'name' => 'admin::app.acl.view',
      'route' => 'admin.products.view',
      'sort' => 3,
    ),
    55 => 
    array (
      'key' => 'settings',
      'name' => 'admin::app.acl.settings',
      'route' => 'admin.settings.index',
      'sort' => 8,
    ),
    56 => 
    array (
      'key' => 'settings.user',
      'name' => 'admin::app.acl.user',
      'route' => 
      array (
        0 => 'admin.settings.groups.index',
        1 => 'admin.settings.roles.index',
        2 => 'admin.settings.users.index',
      ),
      'sort' => 1,
    ),
    57 => 
    array (
      'key' => 'settings.user.groups',
      'name' => 'admin::app.acl.groups',
      'route' => 'admin.settings.groups.index',
      'sort' => 1,
    ),
    58 => 
    array (
      'key' => 'settings.user.groups.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.groups.create',
        1 => 'admin.settings.groups.store',
      ),
      'sort' => 1,
    ),
    59 => 
    array (
      'key' => 'settings.user.groups.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.groups.edit',
        1 => 'admin.settings.groups.update',
      ),
      'sort' => 2,
    ),
    60 => 
    array (
      'key' => 'settings.user.groups.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 'admin.settings.groups.delete',
      'sort' => 3,
    ),
    61 => 
    array (
      'key' => 'settings.user.roles',
      'name' => 'admin::app.acl.roles',
      'route' => 'admin.settings.roles.index',
      'sort' => 2,
    ),
    62 => 
    array (
      'key' => 'settings.user.roles.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.roles.create',
        1 => 'admin.settings.roles.store',
      ),
      'sort' => 1,
    ),
    63 => 
    array (
      'key' => 'settings.user.roles.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.roles.edit',
        1 => 'admin.settings.roles.update',
      ),
      'sort' => 2,
    ),
    64 => 
    array (
      'key' => 'settings.user.roles.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 'admin.settings.roles.delete',
      'sort' => 3,
    ),
    65 => 
    array (
      'key' => 'settings.user.users',
      'name' => 'admin::app.acl.users',
      'route' => 'admin.settings.users.index',
      'sort' => 3,
    ),
    66 => 
    array (
      'key' => 'settings.user.users.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.users.create',
        1 => 'admin.settings.users.store',
      ),
      'sort' => 1,
    ),
    67 => 
    array (
      'key' => 'settings.user.users.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.users.edit',
        1 => 'admin.settings.users.update',
        2 => 'admin.settings.users.mass_update',
      ),
      'sort' => 2,
    ),
    68 => 
    array (
      'key' => 'settings.user.users.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 
      array (
        0 => 'admin.settings.users.delete',
        1 => 'admin.settings.users.mass_delete',
      ),
      'sort' => 3,
    ),
    69 => 
    array (
      'key' => 'settings.lead',
      'name' => 'admin::app.acl.lead',
      'route' => 
      array (
        0 => 'admin.settings.pipelines.index',
        1 => 'admin.settings.sources.index',
        2 => 'admin.settings.types.index',
      ),
      'sort' => 2,
    ),
    70 => 
    array (
      'key' => 'settings.lead.pipelines',
      'name' => 'admin::app.acl.pipelines',
      'route' => 'admin.settings.pipelines.index',
      'sort' => 1,
    ),
    71 => 
    array (
      'key' => 'settings.lead.pipelines.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.pipelines.create',
        1 => 'admin.settings.pipelines.store',
      ),
      'sort' => 1,
    ),
    72 => 
    array (
      'key' => 'settings.lead.pipelines.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.pipelines.edit',
        1 => 'admin.settings.pipelines.update',
      ),
      'sort' => 2,
    ),
    73 => 
    array (
      'key' => 'settings.lead.pipelines.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 'admin.settings.pipelines.delete',
      'sort' => 3,
    ),
    74 => 
    array (
      'key' => 'settings.lead.sources',
      'name' => 'admin::app.acl.sources',
      'route' => 'admin.settings.sources.index',
      'sort' => 2,
    ),
    75 => 
    array (
      'key' => 'settings.lead.sources.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.sources.store',
      ),
      'sort' => 1,
    ),
    76 => 
    array (
      'key' => 'settings.lead.sources.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.sources.edit',
        1 => 'admin.settings.sources.update',
      ),
      'sort' => 2,
    ),
    77 => 
    array (
      'key' => 'settings.lead.sources.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 'admin.settings.sources.delete',
      'sort' => 3,
    ),
    78 => 
    array (
      'key' => 'settings.lead.types',
      'name' => 'admin::app.acl.types',
      'route' => 'admin.settings.types.index',
      'sort' => 3,
    ),
    79 => 
    array (
      'key' => 'settings.lead.types.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.types.store',
      ),
      'sort' => 1,
    ),
    80 => 
    array (
      'key' => 'settings.lead.types.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.types.edit',
        1 => 'admin.settings.types.update',
      ),
      'sort' => 2,
    ),
    81 => 
    array (
      'key' => 'settings.lead.types.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 'admin.settings.types.delete',
      'sort' => 3,
    ),
    82 => 
    array (
      'key' => 'settings.automation',
      'name' => 'admin::app.acl.automation',
      'route' => 
      array (
        0 => 'admin.settings.attributes.index',
        1 => 'admin.settings.email_templates.index',
        2 => 'admin.settings.workflows.index',
      ),
      'sort' => 3,
    ),
    83 => 
    array (
      'key' => 'settings.automation.attributes',
      'name' => 'admin::app.acl.attributes',
      'route' => 'admin.settings.attributes.index',
      'sort' => 1,
    ),
    84 => 
    array (
      'key' => 'settings.automation.attributes.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.attributes.create',
        1 => 'admin.settings.attributes.store',
      ),
      'sort' => 1,
    ),
    85 => 
    array (
      'key' => 'settings.automation.attributes.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.attributes.edit',
        1 => 'admin.settings.attributes.update',
        2 => 'admin.settings.attributes.mass_update',
      ),
      'sort' => 2,
    ),
    86 => 
    array (
      'key' => 'settings.automation.attributes.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 'admin.settings.attributes.delete',
      'sort' => 3,
    ),
    87 => 
    array (
      'key' => 'settings.automation.email_templates',
      'name' => 'admin::app.acl.email-templates',
      'route' => 'admin.settings.email_templates.index',
      'sort' => 7,
    ),
    88 => 
    array (
      'key' => 'settings.automation.email_templates.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.email_templates.create',
        1 => 'admin.settings.email_templates.store',
      ),
      'sort' => 1,
    ),
    89 => 
    array (
      'key' => 'settings.automation.email_templates.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.email_templates.edit',
        1 => 'admin.settings.email_templates.update',
      ),
      'sort' => 2,
    ),
    90 => 
    array (
      'key' => 'settings.automation.email_templates.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 'admin.settings.email_templates.delete',
      'sort' => 3,
    ),
    91 => 
    array (
      'key' => 'settings.automation.workflows',
      'name' => 'admin::app.acl.workflows',
      'route' => 'admin.settings.workflows.index',
      'sort' => 2,
    ),
    92 => 
    array (
      'key' => 'settings.automation.workflows.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.workflows.create',
        1 => 'admin.settings.workflows.store',
      ),
      'sort' => 1,
    ),
    93 => 
    array (
      'key' => 'settings.automation.workflows.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.workflows.edit',
        1 => 'admin.settings.workflows.update',
      ),
      'sort' => 2,
    ),
    94 => 
    array (
      'key' => 'settings.automation.workflows.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 'admin.settings.workflows.delete',
      'sort' => 3,
    ),
    95 => 
    array (
      'key' => 'settings.automation.events',
      'name' => 'admin::app.acl.event',
      'route' => 'admin.settings.marketing.events.index',
      'sort' => 2,
    ),
    96 => 
    array (
      'key' => 'settings.automation.events.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.marketing.events.create',
        1 => 'admin.settings.marketing.events.store',
      ),
      'sort' => 1,
    ),
    97 => 
    array (
      'key' => 'settings.automation.events.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.marketing.events.edit',
        1 => 'admin.settings.marketing.events.update',
      ),
      'sort' => 2,
    ),
    98 => 
    array (
      'key' => 'settings.automation.events.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 
      array (
        0 => 'admin.settings.marketing.events.delete',
        1 => 'admin.settings.marketing.events.mass_delete',
      ),
      'sort' => 3,
    ),
    99 => 
    array (
      'key' => 'settings.automation.campaigns',
      'name' => 'admin::app.acl.campaigns',
      'route' => 'admin.settings.marketing.campaigns.index',
      'sort' => 2,
    ),
    100 => 
    array (
      'key' => 'settings.automation.campaigns.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.marketing.campaigns.create',
        1 => 'admin.settings.marketing.campaigns.store',
      ),
      'sort' => 1,
    ),
    101 => 
    array (
      'key' => 'settings.automation.campaigns.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.marketing.campaigns.edit',
        1 => 'admin.settings.marketing.campaigns.update',
      ),
      'sort' => 2,
    ),
    102 => 
    array (
      'key' => 'settings.automation.campaigns.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 
      array (
        0 => 'admin.settings.marketing.campaigns.delete',
        1 => 'admin.settings.marketing.campaigns.mass_delete',
      ),
      'sort' => 3,
    ),
    103 => 
    array (
      'key' => 'settings.automation.webhooks',
      'name' => 'admin::app.acl.webhook',
      'route' => 'admin.settings.webhooks.index',
      'sort' => 1,
    ),
    104 => 
    array (
      'key' => 'settings.automation.webhooks.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.webhooks.create',
        1 => 'admin.settings.webhooks.store',
      ),
      'sort' => 1,
    ),
    105 => 
    array (
      'key' => 'settings.automation.webhooks.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.webhooks.edit',
        1 => 'admin.settings.webhooks.update',
      ),
      'sort' => 2,
    ),
    106 => 
    array (
      'key' => 'settings.automation.webhooks.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 'admin.settings.webhooks.delete',
      'sort' => 3,
    ),
    107 => 
    array (
      'key' => 'settings.other_settings',
      'name' => 'admin::app.acl.other-settings',
      'route' => 'admin.settings.tags.index',
      'sort' => 4,
    ),
    108 => 
    array (
      'key' => 'settings.other_settings.tags',
      'name' => 'admin::app.acl.tags',
      'route' => 'admin.settings.tags.index',
      'sort' => 1,
    ),
    109 => 
    array (
      'key' => 'settings.other_settings.tags.create',
      'name' => 'admin::app.acl.create',
      'route' => 
      array (
        0 => 'admin.settings.tags.create',
        1 => 'admin.settings.tags.store',
        2 => 'admin.leads.tags.attach',
      ),
      'sort' => 1,
    ),
    110 => 
    array (
      'key' => 'settings.other_settings.tags.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 
      array (
        0 => 'admin.settings.tags.edit',
        1 => 'admin.settings.tags.update',
      ),
      'sort' => 1,
    ),
    111 => 
    array (
      'key' => 'settings.other_settings.tags.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 
      array (
        0 => 'admin.settings.tags.delete',
        1 => 'admin.settings.tags.mass_delete',
        2 => 'admin.leads.tags.detach',
      ),
      'sort' => 2,
    ),
    112 => 
    array (
      'key' => 'settings.data_transfer',
      'name' => 'admin::app.acl.data-transfer',
      'route' => 'admin.settings.data_transfer.imports.index',
      'sort' => 10,
    ),
    113 => 
    array (
      'key' => 'settings.data_transfer.imports',
      'name' => 'admin::app.acl.imports',
      'route' => 'admin.settings.data_transfer.imports.index',
      'sort' => 1,
    ),
    114 => 
    array (
      'key' => 'settings.data_transfer.imports.create',
      'name' => 'admin::app.acl.create',
      'route' => 'admin.settings.data_transfer.imports.create',
      'sort' => 1,
    ),
    115 => 
    array (
      'key' => 'settings.data_transfer.imports.edit',
      'name' => 'admin::app.acl.edit',
      'route' => 'admin.settings.data_transfer.imports.edit',
      'sort' => 2,
    ),
    116 => 
    array (
      'key' => 'settings.data_transfer.imports.delete',
      'name' => 'admin::app.acl.delete',
      'route' => 'admin.settings.data_transfer.imports.delete',
      'sort' => 3,
    ),
    117 => 
    array (
      'key' => 'settings.data_transfer.imports.import',
      'name' => 'admin::app.acl.import',
      'route' => 'admin.settings.data_transfer.imports.imports',
      'sort' => 4,
    ),
    118 => 
    array (
      'key' => 'configuration',
      'name' => 'admin::app.acl.configuration',
      'route' => 'admin.configuration.index',
      'sort' => 9,
    ),
  ),
  'menu' => 
  array (
    'admin' => 
    array (
      0 => 
      array (
        'key' => 'sales',
        'name' => 'sales::app.layouts.sales',
        'route' => 'admin.sales.dashboard.index',
        'sort' => 3,
        'icon-class' => 'icon-sales',
      ),
      1 => 
      array (
        'key' => 'sales.targets',
        'name' => 'sales::app.layouts.targets',
        'route' => 'admin.sales.targets.index',
        'sort' => 1,
        'icon-class' => '',
      ),
      2 => 
      array (
        'key' => 'sales.performance',
        'name' => 'sales::app.layouts.performance',
        'route' => 'admin.sales.performance.index',
        'sort' => 2,
        'icon-class' => '',
      ),
      3 => 
      array (
        'key' => 'sales.reports',
        'name' => 'sales::app.layouts.reports',
        'route' => 'admin.sales.reports.index',
        'sort' => 3,
        'icon-class' => '',
      ),
      4 => 
      array (
        'key' => 'settings.other_settings.web_forms',
        'name' => 'web_form::app.menu.title',
        'info' => 'web_form::app.menu.title-info',
        'route' => 'admin.settings.web_forms.index',
        'sort' => 1,
        'icon-class' => 'icon-settings-webforms',
      ),
      5 => 
      array (
        'key' => 'dashboard',
        'name' => 'admin::app.layouts.dashboard',
        'route' => 'admin.dashboard.index',
        'sort' => 1,
        'icon-class' => 'icon-dashboard',
      ),
      6 => 
      array (
        'key' => 'leads',
        'name' => 'admin::app.layouts.leads',
        'route' => 'admin.leads.index',
        'sort' => 2,
        'icon-class' => 'icon-leads',
      ),
      7 => 
      array (
        'key' => 'quotes',
        'name' => 'admin::app.layouts.quotes',
        'route' => 'admin.quotes.index',
        'sort' => 3,
        'icon-class' => 'icon-quote',
      ),
      8 => 
      array (
        'key' => 'mail',
        'name' => 'admin::app.layouts.mail.title',
        'route' => 'admin.mail.index',
        'params' => 
        array (
          'route' => 'inbox',
        ),
        'sort' => 4,
        'icon-class' => 'icon-mail',
      ),
      9 => 
      array (
        'key' => 'mail.inbox',
        'name' => 'admin::app.layouts.mail.inbox',
        'route' => 'admin.mail.index',
        'params' => 
        array (
          'route' => 'inbox',
        ),
        'sort' => 2,
        'icon-class' => '',
      ),
      10 => 
      array (
        'key' => 'mail.draft',
        'name' => 'admin::app.layouts.mail.draft',
        'route' => 'admin.mail.index',
        'params' => 
        array (
          'route' => 'draft',
        ),
        'sort' => 3,
        'icon-class' => '',
      ),
      11 => 
      array (
        'key' => 'mail.outbox',
        'name' => 'admin::app.layouts.mail.outbox',
        'route' => 'admin.mail.index',
        'params' => 
        array (
          'route' => 'outbox',
        ),
        'sort' => 4,
        'icon-class' => '',
      ),
      12 => 
      array (
        'key' => 'mail.sent',
        'name' => 'admin::app.layouts.mail.sent',
        'route' => 'admin.mail.index',
        'params' => 
        array (
          'route' => 'sent',
        ),
        'sort' => 4,
        'icon-class' => '',
      ),
      13 => 
      array (
        'key' => 'mail.trash',
        'name' => 'admin::app.layouts.mail.trash',
        'route' => 'admin.mail.index',
        'params' => 
        array (
          'route' => 'trash',
        ),
        'sort' => 5,
        'icon-class' => '',
      ),
      14 => 
      array (
        'key' => 'activities',
        'name' => 'admin::app.layouts.activities',
        'route' => 'admin.activities.index',
        'sort' => 5,
        'icon-class' => 'icon-activity',
      ),
      15 => 
      array (
        'key' => 'contacts',
        'name' => 'admin::app.layouts.contacts',
        'route' => 'admin.contacts.persons.index',
        'sort' => 6,
        'icon-class' => 'icon-contact',
      ),
      16 => 
      array (
        'key' => 'contacts.persons',
        'name' => 'admin::app.layouts.persons',
        'route' => 'admin.contacts.persons.index',
        'sort' => 1,
        'icon-class' => '',
      ),
      17 => 
      array (
        'key' => 'contacts.organizations',
        'name' => 'admin::app.layouts.organizations',
        'route' => 'admin.contacts.organizations.index',
        'sort' => 2,
        'icon-class' => '',
      ),
      18 => 
      array (
        'key' => 'products',
        'name' => 'admin::app.layouts.products',
        'route' => 'admin.products.index',
        'sort' => 7,
        'icon-class' => 'icon-product',
      ),
      19 => 
      array (
        'key' => 'settings',
        'name' => 'admin::app.layouts.settings',
        'route' => 'admin.settings.index',
        'sort' => 8,
        'icon-class' => 'icon-setting',
      ),
      20 => 
      array (
        'key' => 'settings.user',
        'name' => 'admin::app.layouts.user',
        'route' => 'admin.settings.groups.index',
        'info' => 'admin::app.layouts.user-info',
        'sort' => 1,
        'icon-class' => 'icon-settings-group',
      ),
      21 => 
      array (
        'key' => 'settings.user.groups',
        'name' => 'admin::app.layouts.groups',
        'info' => 'admin::app.layouts.groups-info',
        'route' => 'admin.settings.groups.index',
        'sort' => 1,
        'icon-class' => 'icon-settings-group',
      ),
      22 => 
      array (
        'key' => 'settings.user.roles',
        'name' => 'admin::app.layouts.roles',
        'info' => 'admin::app.layouts.roles-info',
        'route' => 'admin.settings.roles.index',
        'sort' => 2,
        'icon-class' => 'icon-role',
      ),
      23 => 
      array (
        'key' => 'settings.user.users',
        'name' => 'admin::app.layouts.users',
        'info' => 'admin::app.layouts.users-info',
        'route' => 'admin.settings.users.index',
        'sort' => 3,
        'icon-class' => 'icon-user',
      ),
      24 => 
      array (
        'key' => 'settings.lead',
        'name' => 'admin::app.layouts.lead',
        'info' => 'admin::app.layouts.lead-info',
        'route' => 'admin.settings.pipelines.index',
        'sort' => 2,
        'icon-class' => '',
      ),
      25 => 
      array (
        'key' => 'settings.lead.pipelines',
        'name' => 'admin::app.layouts.pipelines',
        'info' => 'admin::app.layouts.pipelines-info',
        'route' => 'admin.settings.pipelines.index',
        'sort' => 1,
        'icon-class' => 'icon-settings-pipeline',
      ),
      26 => 
      array (
        'key' => 'settings.lead.sources',
        'name' => 'admin::app.layouts.sources',
        'info' => 'admin::app.layouts.sources-info',
        'route' => 'admin.settings.sources.index',
        'sort' => 2,
        'icon-class' => 'icon-settings-sources',
      ),
      27 => 
      array (
        'key' => 'settings.lead.types',
        'name' => 'admin::app.layouts.types',
        'info' => 'admin::app.layouts.types-info',
        'route' => 'admin.settings.types.index',
        'sort' => 3,
        'icon-class' => 'icon-settings-type',
      ),
      28 => 
      array (
        'key' => 'settings.warehouse',
        'name' => 'admin::app.layouts.warehouse',
        'info' => 'admin::app.layouts.warehouses-info',
        'route' => 'admin.settings.pipelines.index',
        'icon-class' => '',
        'sort' => 2,
      ),
      29 => 
      array (
        'key' => 'settings.warehouse.warehouses',
        'name' => 'admin::app.layouts.warehouses',
        'info' => 'admin::app.layouts.warehouses-info',
        'route' => 'admin.settings.warehouses.index',
        'sort' => 1,
        'icon-class' => 'icon-settings-warehouse',
      ),
      30 => 
      array (
        'key' => 'settings.automation',
        'name' => 'admin::app.layouts.automation',
        'info' => 'admin::app.layouts.automation-info',
        'route' => 'admin.settings.attributes.index',
        'sort' => 3,
        'icon-class' => '',
      ),
      31 => 
      array (
        'key' => 'settings.automation.attributes',
        'name' => 'admin::app.layouts.attributes',
        'info' => 'admin::app.layouts.attributes-info',
        'route' => 'admin.settings.attributes.index',
        'sort' => 1,
        'icon-class' => 'icon-attribute',
      ),
      32 => 
      array (
        'key' => 'settings.automation.email_templates',
        'name' => 'admin::app.layouts.email-templates',
        'info' => 'admin::app.layouts.email-templates-info',
        'route' => 'admin.settings.email_templates.index',
        'sort' => 2,
        'icon-class' => 'icon-settings-mail',
      ),
      33 => 
      array (
        'key' => 'settings.automation.events',
        'name' => 'admin::app.layouts.events',
        'info' => 'admin::app.layouts.events-info',
        'route' => 'admin.settings.marketing.events.index',
        'sort' => 2,
        'icon-class' => 'icon-calendar',
      ),
      34 => 
      array (
        'key' => 'settings.automation.campaigns',
        'name' => 'admin::app.layouts.campaigns',
        'info' => 'admin::app.layouts.campaigns-info',
        'route' => 'admin.settings.marketing.campaigns.index',
        'sort' => 2,
        'icon-class' => 'icon-note',
      ),
      35 => 
      array (
        'key' => 'settings.automation.webhooks',
        'name' => 'admin::app.layouts.webhooks',
        'info' => 'admin::app.layouts.webhooks-info',
        'route' => 'admin.settings.webhooks.index',
        'sort' => 2,
        'icon-class' => 'icon-settings-webhooks',
      ),
      36 => 
      array (
        'key' => 'settings.automation.workflows',
        'name' => 'admin::app.layouts.workflows',
        'info' => 'admin::app.layouts.workflows-info',
        'route' => 'admin.settings.workflows.index',
        'sort' => 3,
        'icon-class' => 'icon-settings-flow',
      ),
      37 => 
      array (
        'key' => 'settings.automation.data_transfer',
        'name' => 'admin::app.layouts.data_transfer',
        'info' => 'admin::app.layouts.data_transfer_info',
        'route' => 'admin.settings.data_transfer.imports.index',
        'sort' => 4,
        'icon-class' => 'icon-download',
      ),
      38 => 
      array (
        'key' => 'settings.other_settings',
        'name' => 'admin::app.layouts.other-settings',
        'info' => 'admin::app.layouts.other-settings-info',
        'route' => 'admin.settings.tags.index',
        'sort' => 4,
        'icon-class' => 'icon-settings',
      ),
      39 => 
      array (
        'key' => 'settings.other_settings.tags',
        'name' => 'admin::app.layouts.tags',
        'info' => 'admin::app.layouts.tags-info',
        'route' => 'admin.settings.tags.index',
        'sort' => 1,
        'icon-class' => 'icon-settings-tag',
      ),
      40 => 
      array (
        'key' => 'configuration',
        'name' => 'admin::app.layouts.configuration',
        'route' => 'admin.configuration.index',
        'sort' => 9,
        'icon-class' => 'icon-configuration',
      ),
    ),
  ),
  'core_config' => 
  array (
    0 => 
    array (
      'key' => 'general',
      'name' => 'admin::app.configuration.index.general.title',
      'info' => 'admin::app.configuration.index.general.info',
      'sort' => 1,
    ),
    1 => 
    array (
      'key' => 'general.general',
      'name' => 'admin::app.configuration.index.general.general.title',
      'info' => 'admin::app.configuration.index.general.general.info',
      'icon' => 'icon-setting',
      'sort' => 1,
    ),
    2 => 
    array (
      'key' => 'general.general.locale_settings',
      'name' => 'admin::app.configuration.index.general.general.locale-settings.title',
      'info' => 'admin::app.configuration.index.general.general.locale-settings.title-info',
      'sort' => 1,
      'fields' => 
      array (
        0 => 
        array (
          'name' => 'locale',
          'title' => 'admin::app.configuration.index.general.general.locale-settings.title',
          'type' => 'select',
          'default' => 'en',
          'options' => 'Webkul\\Core\\Core@locales',
        ),
      ),
    ),
    3 => 
    array (
      'key' => 'general.general.admin_logo',
      'name' => 'admin::app.configuration.index.general.general.admin-logo.title',
      'info' => 'admin::app.configuration.index.general.general.admin-logo.title-info',
      'sort' => 2,
      'fields' => 
      array (
        0 => 
        array (
          'name' => 'logo_image',
          'title' => 'admin::app.configuration.index.general.general.admin-logo.logo-image',
          'type' => 'image',
          'validation' => 'mimes:bmp,jpeg,jpg,png,webp,svg',
        ),
      ),
    ),
    4 => 
    array (
      'key' => 'general.settings',
      'name' => 'admin::app.configuration.index.general.settings.title',
      'info' => 'admin::app.configuration.index.general.settings.info',
      'icon' => 'icon-configuration',
      'sort' => 2,
    ),
    5 => 
    array (
      'key' => 'general.settings.footer',
      'name' => 'admin::app.configuration.index.general.settings.footer.title',
      'info' => 'admin::app.configuration.index.general.settings.footer.info',
      'sort' => 1,
      'fields' => 
      array (
        0 => 
        array (
          'name' => 'label',
          'title' => 'admin::app.configuration.index.general.settings.footer.powered-by',
          'type' => 'editor',
          'default' => 'Powered by <span style="color: rgb(14, 144, 217);"><a href="http://www.krayincrm.com" target="_blank">Krayin</a></span>, an open-source project by <span style="color: rgb(14, 144, 217);"><a href="https://webkul.com" target="_blank">Webkul</a></span>.',
          'tinymce' => true,
        ),
      ),
    ),
    6 => 
    array (
      'key' => 'general.settings.menu',
      'name' => 'admin::app.configuration.index.general.settings.menu.title',
      'info' => 'admin::app.configuration.index.general.settings.menu.info',
      'sort' => 2,
      'fields' => 
      array (
        0 => 
        array (
          'name' => 'dashboard',
          'title' => 'admin::app.configuration.index.general.settings.menu.dashboard',
          'type' => 'text',
          'default' => 'Dashboard',
          'validation' => 'max:20',
        ),
        1 => 
        array (
          'name' => 'leads',
          'title' => 'admin::app.configuration.index.general.settings.menu.leads',
          'type' => 'text',
          'default' => 'Leads',
          'validation' => 'max:20',
        ),
        2 => 
        array (
          'name' => 'quotes',
          'title' => 'admin::app.configuration.index.general.settings.menu.quotes',
          'type' => 'text',
          'default' => 'Quotes',
          'validation' => 'max:20',
        ),
        3 => 
        array (
          'name' => 'mail.mail',
          'title' => 'admin::app.configuration.index.general.settings.menu.mail',
          'type' => 'text',
          'default' => 'Mail',
          'validation' => 'max:20',
        ),
        4 => 
        array (
          'name' => 'mail.inbox',
          'title' => 'admin::app.configuration.index.general.settings.menu.inbox',
          'type' => 'text',
          'default' => 'Inbox',
          'validation' => 'max:20',
        ),
        5 => 
        array (
          'name' => 'mail.draft',
          'title' => 'admin::app.configuration.index.general.settings.menu.draft',
          'type' => 'text',
          'default' => 'Draft',
          'validation' => 'max:20',
        ),
        6 => 
        array (
          'name' => 'mail.outbox',
          'title' => 'admin::app.configuration.index.general.settings.menu.outbox',
          'type' => 'text',
          'default' => 'Outbox',
          'validation' => 'max:20',
        ),
        7 => 
        array (
          'name' => 'mail.sent',
          'title' => 'admin::app.configuration.index.general.settings.menu.sent',
          'type' => 'text',
          'default' => 'Sent',
          'validation' => 'max:20',
        ),
        8 => 
        array (
          'name' => 'mail.trash',
          'title' => 'admin::app.configuration.index.general.settings.menu.trash',
          'type' => 'text',
          'default' => 'Trash',
          'validation' => 'max:20',
        ),
        9 => 
        array (
          'name' => 'activities',
          'title' => 'admin::app.configuration.index.general.settings.menu.activities',
          'type' => 'text',
          'default' => 'Activities',
          'validation' => 'max:20',
        ),
        10 => 
        array (
          'name' => 'contacts.contacts',
          'title' => 'admin::app.configuration.index.general.settings.menu.contacts',
          'type' => 'text',
          'default' => 'Contacts',
          'validation' => 'max:20',
        ),
        11 => 
        array (
          'name' => 'contacts.persons',
          'title' => 'admin::app.configuration.index.general.settings.menu.persons',
          'type' => 'text',
          'default' => 'Persons',
          'validation' => 'max:20',
        ),
        12 => 
        array (
          'name' => 'contacts.organizations',
          'title' => 'admin::app.configuration.index.general.settings.menu.organizations',
          'type' => 'text',
          'default' => 'Organizations',
          'validation' => 'max:20',
        ),
        13 => 
        array (
          'name' => 'products',
          'title' => 'admin::app.configuration.index.general.settings.menu.products',
          'type' => 'text',
          'default' => 'Products',
          'validation' => 'max:20',
        ),
        14 => 
        array (
          'name' => 'settings',
          'title' => 'admin::app.configuration.index.general.settings.menu.settings',
          'type' => 'text',
          'default' => 'Settings',
          'validation' => 'max:20',
        ),
        15 => 
        array (
          'name' => 'configuration',
          'title' => 'admin::app.configuration.index.general.settings.menu.configuration',
          'type' => 'text',
          'default' => 'Configuration',
          'validation' => 'max:20',
        ),
      ),
    ),
    7 => 
    array (
      'key' => 'general.settings.menu_color',
      'name' => 'admin::app.configuration.index.general.settings.menu-color.title',
      'info' => 'admin::app.configuration.index.general.settings.menu-color.info',
      'sort' => 3,
      'fields' => 
      array (
        0 => 
        array (
          'name' => 'brand_color',
          'title' => 'admin::app.configuration.index.general.settings.menu-color.brand-color',
          'type' => 'color',
          'default' => '#0E90D9',
        ),
      ),
    ),
    8 => 
    array (
      'key' => 'general.magic_ai',
      'name' => 'admin::app.configuration.index.magic-ai.title',
      'info' => 'admin::app.configuration.index.magic-ai.info',
      'icon' => 'icon-setting',
      'sort' => 3,
    ),
    9 => 
    array (
      'key' => 'general.magic_ai.settings',
      'name' => 'admin::app.configuration.index.magic-ai.settings.title',
      'info' => 'admin::app.configuration.index.magic-ai.settings.info',
      'sort' => 1,
      'fields' => 
      array (
        0 => 
        array (
          'name' => 'enable',
          'title' => 'admin::app.configuration.index.magic-ai.settings.enable',
          'type' => 'boolean',
          'channel_based' => true,
        ),
        1 => 
        array (
          'name' => 'api_key',
          'title' => 'admin::app.configuration.index.magic-ai.settings.api-key',
          'type' => 'password',
          'depends' => 'enable:1',
          'validation' => 'required_if:enable,1',
          'info' => 'admin::app.configuration.index.magic-ai.settings.api-key-info',
        ),
        2 => 
        array (
          'name' => 'model',
          'title' => 'admin::app.configuration.index.magic-ai.settings.models.title',
          'type' => 'select',
          'channel_based' => true,
          'depends' => 'enable:1',
          'options' => 
          array (
            0 => 
            array (
              'title' => 'admin::app.configuration.index.magic-ai.settings.models.gpt-4o',
              'value' => 'openai/chatgpt-4o-latest',
            ),
            1 => 
            array (
              'title' => 'admin::app.configuration.index.magic-ai.settings.models.gpt-4o-mini',
              'value' => 'openai/gpt-4o-mini',
            ),
            2 => 
            array (
              'title' => 'admin::app.configuration.index.magic-ai.settings.models.gemini-2-0-flash-001',
              'value' => 'google/gemini-2.0-flash-001',
            ),
            3 => 
            array (
              'title' => 'admin::app.configuration.index.magic-ai.settings.models.deepseek-r1',
              'value' => 'deepseek/deepseek-r1-distill-llama-8b',
            ),
            4 => 
            array (
              'title' => 'admin::app.configuration.index.magic-ai.settings.models.llama-3-2-3b-instruct',
              'value' => 'meta-llama/llama-3.2-3b-instruct',
            ),
            5 => 
            array (
              'title' => 'admin::app.configuration.index.magic-ai.settings.models.grok-2-1212',
              'value' => 'x-ai/grok-2-1212',
            ),
          ),
        ),
        3 => 
        array (
          'name' => 'other_model',
          'title' => 'admin::app.configuration.index.magic-ai.settings.other',
          'type' => 'text',
          'info' => 'admin::app.configuration.index.magic-ai.settings.other-model',
          'default' => NULL,
          'depends' => 'enable:1',
        ),
      ),
    ),
    10 => 
    array (
      'key' => 'general.magic_ai.doc_generation',
      'name' => 'admin::app.configuration.index.magic-ai.settings.doc-generation',
      'info' => 'admin::app.configuration.index.magic-ai.settings.doc-generation-info',
      'sort' => 2,
      'fields' => 
      array (
        0 => 
        array (
          'name' => 'enabled',
          'title' => 'admin::app.configuration.index.magic-ai.settings.enable',
          'type' => 'boolean',
        ),
      ),
    ),
    11 => 
    array (
      'key' => 'email',
      'name' => 'admin::app.configuration.index.email.title',
      'info' => 'admin::app.configuration.index.email.info',
      'sort' => 2,
    ),
    12 => 
    array (
      'key' => 'email.imap',
      'name' => 'admin::app.configuration.index.email.imap.title',
      'info' => 'admin::app.configuration.index.email.imap.info',
      'icon' => 'icon-setting',
      'sort' => 1,
    ),
    13 => 
    array (
      'key' => 'email.imap.account',
      'name' => 'admin::app.configuration.index.email.imap.account.title',
      'info' => 'admin::app.configuration.index.email.imap.account.title-info',
      'sort' => 1,
      'fields' => 
      array (
        0 => 
        array (
          'name' => 'host',
          'title' => 'admin::app.configuration.index.email.imap.account.host',
          'type' => 'text',
          'default' => 'localhost',
        ),
        1 => 
        array (
          'name' => 'port',
          'title' => 'admin::app.configuration.index.email.imap.account.port',
          'type' => 'text',
          'default' => 993,
        ),
        2 => 
        array (
          'name' => 'encryption',
          'title' => 'admin::app.configuration.index.email.imap.account.encryption',
          'type' => 'text',
          'default' => 'ssl',
        ),
        3 => 
        array (
          'name' => 'validate_cert',
          'title' => 'admin::app.configuration.index.email.imap.account.validate-cert',
          'type' => 'boolean',
          'default' => true,
        ),
        4 => 
        array (
          'name' => 'username',
          'title' => 'admin::app.configuration.index.email.imap.account.username',
          'type' => 'text',
          'default' => '<EMAIL>',
        ),
        5 => 
        array (
          'name' => 'password',
          'title' => 'admin::app.configuration.index.email.imap.account.password',
          'type' => 'password',
          'default' => '',
        ),
      ),
    ),
  ),
  'attribute_lookups' => 
  array (
    'leads' => 
    array (
      'name' => 'Leads',
      'repository' => 'Webkul\\Lead\\Repositories\\LeadRepository',
      'label_column' => 'title',
    ),
    'lead_sources' => 
    array (
      'name' => 'Lead Sources',
      'repository' => 'Webkul\\Lead\\Repositories\\SourceRepository',
    ),
    'lead_types' => 
    array (
      'name' => 'Lead Types',
      'repository' => 'Webkul\\Lead\\Repositories\\TypeRepository',
    ),
    'lead_pipelines' => 
    array (
      'name' => 'Lead Pipelines',
      'repository' => 'Webkul\\Lead\\Repositories\\PipelineRepository',
    ),
    'lead_pipeline_stages' => 
    array (
      'name' => 'Lead Pipeline Stages',
      'repository' => 'Webkul\\Lead\\Repositories\\StageRepository',
    ),
    'users' => 
    array (
      'name' => 'Sales Owners',
      'repository' => 'Webkul\\User\\Repositories\\UserRepository',
    ),
    'organizations' => 
    array (
      'name' => 'Organizations',
      'repository' => 'Webkul\\Contact\\Repositories\\OrganizationRepository',
    ),
    'persons' => 
    array (
      'name' => 'Persons',
      'repository' => 'Webkul\\Contact\\Repositories\\PersonRepository',
    ),
    'warehouses' => 
    array (
      'name' => 'Warehouses',
      'repository' => 'Webkul\\Warehouse\\Repositories\\WarehouseRepository',
    ),
    'locations' => 
    array (
      'name' => 'Locations',
      'repository' => 'Webkul\\Warehouse\\Repositories\\LocationRepository',
    ),
  ),
  'attribute_entity_types' => 
  array (
    'leads' => 
    array (
      'name' => 'admin::app.leads.index.title',
      'repository' => 'Webkul\\Lead\\Repositories\\LeadRepository',
    ),
    'persons' => 
    array (
      'name' => 'admin::app.contacts.persons.index.title',
      'repository' => 'Webkul\\Contact\\Repositories\\PersonRepository',
    ),
    'organizations' => 
    array (
      'name' => 'admin::app.contacts.organizations.index.title',
      'repository' => 'Webkul\\Contact\\Repositories\\OrganizationRepository',
    ),
    'products' => 
    array (
      'name' => 'admin::app.products.index.title',
      'repository' => 'Webkul\\Product\\Repositories\\ProductRepository',
    ),
    'quotes' => 
    array (
      'name' => 'admin::app.quotes.index.title',
      'repository' => 'Webkul\\Quote\\Repositories\\QuoteRepository',
    ),
    'warehouses' => 
    array (
      'name' => 'admin::app.settings.warehouses.index.title',
      'repository' => 'Webkul\\Warehouse\\Repositories\\WarehouseRepository',
    ),
  ),
  'workflows' => 
  array (
    'trigger_entities' => 
    array (
      'leads' => 
      array (
        'name' => 'Leads',
        'class' => 'Webkul\\Automation\\Helpers\\Entity\\Lead',
        'events' => 
        array (
          0 => 
          array (
            'event' => 'lead.create.after',
            'name' => 'Created',
          ),
          1 => 
          array (
            'event' => 'lead.update.after',
            'name' => 'Updated',
          ),
          2 => 
          array (
            'event' => 'lead.delete.before',
            'name' => 'Deleted',
          ),
        ),
      ),
      'activities' => 
      array (
        'name' => 'Activities',
        'class' => 'Webkul\\Automation\\Helpers\\Entity\\Activity',
        'events' => 
        array (
          0 => 
          array (
            'event' => 'activity.create.after',
            'name' => 'Created',
          ),
          1 => 
          array (
            'event' => 'activity.update.after',
            'name' => 'Updated',
          ),
          2 => 
          array (
            'event' => 'activity.delete.before',
            'name' => 'Deleted',
          ),
        ),
      ),
      'persons' => 
      array (
        'name' => 'Persons',
        'class' => 'Webkul\\Automation\\Helpers\\Entity\\Person',
        'events' => 
        array (
          0 => 
          array (
            'event' => 'contacts.person.create.after',
            'name' => 'Created',
          ),
          1 => 
          array (
            'event' => 'contacts.person.update.after',
            'name' => 'Updated',
          ),
          2 => 
          array (
            'event' => 'contacts.person.delete.before',
            'name' => 'Deleted',
          ),
        ),
      ),
      'quotes' => 
      array (
        'name' => 'Quotes',
        'class' => 'Webkul\\Automation\\Helpers\\Entity\\Quote',
        'events' => 
        array (
          0 => 
          array (
            'event' => 'quote.create.after',
            'name' => 'Created',
          ),
          1 => 
          array (
            'event' => 'quote.update.after',
            'name' => 'Updated',
          ),
          2 => 
          array (
            'event' => 'quote.delete.before',
            'name' => 'Deleted',
          ),
        ),
      ),
    ),
  ),
  'importers' => 
  array (
    'persons' => 
    array (
      'title' => 'data_transfer::app.importers.persons.title',
      'importer' => 'Webkul\\DataTransfer\\Helpers\\Importers\\Persons\\Importer',
      'sample_path' => 'data-transfer/samples/persons.csv',
    ),
    'products' => 
    array (
      'title' => 'data_transfer::app.importers.products.title',
      'importer' => 'Webkul\\DataTransfer\\Helpers\\Importers\\Products\\Importer',
      'sample_path' => 'data-transfer/samples/products.csv',
    ),
    'leads' => 
    array (
      'title' => 'data_transfer::app.importers.leads.title',
      'importer' => 'Webkul\\DataTransfer\\Helpers\\Importers\\Leads\\Importer',
      'sample_path' => 'data-transfer/samples/leads.csv',
    ),
  ),
);
